// Test script to verify API endpoints are working correctly
import fs from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:5173/api';

// Test file paths
const testFiles = [
  'examples/invoices/TransIP - F0000.2508.0000.9603.pdf',
  'examples/invoices/Anthropic - TVHUK09C-0001 - Claude Pro 1yr.pdf',
  'examples/invoices/Buckaroo - BUCKEUR0000272352.pdf'
];

async function testUploadEndpoint(filePath) {
  console.log(`\n🧪 Testing upload with: ${path.basename(filePath)}`);
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${filePath}`);
      return null;
    }

    const fileBuffer = fs.readFileSync(filePath);
    const formData = new FormData();
    const blob = new Blob([fileBuffer], { type: 'application/pdf' });
    formData.append('file', blob, path.basename(filePath));

    console.log(`📤 Uploading ${path.basename(filePath)} (${fileBuffer.length} bytes)`);
    
    const response = await fetch(`${API_BASE}/upload`, {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log(`✅ Upload successful`);
      console.log(`   File ID: ${result.fileId}`);
      console.log(`   Thumbnail: ${result.thumbnail ? 'Generated' : 'Not generated'}`);
      return result.fileId;
    } else {
      console.log(`❌ Upload failed: ${result.error || 'Unknown error'}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Upload error: ${error.message}`);
    return null;
  }
}

async function testProcessEndpoint(fileId, fileName) {
  console.log(`\n🔄 Testing processing for: ${fileName}`);
  
  try {
    console.log(`📊 Processing file ID: ${fileId}`);
    
    const response = await fetch(`${API_BASE}/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ fileId }),
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log(`✅ Processing successful`);
      console.log(`   Supplier: ${result.data?.supplier || 'Not extracted'}`);
      console.log(`   Invoice ID: ${result.data?.invoiceId || 'Not extracted'}`);
      console.log(`   Total: ${result.data?.total || 'Not extracted'}`);
      console.log(`   Confidence: ${Math.round((result.metadata?.confidenceScore || 0) * 100)}%`);
      console.log(`   Method: ${result.metadata?.method || 'Unknown'}`);
      console.log(`   Processing Time: ${result.metadata?.processingTime || 0}ms`);
      return result;
    } else {
      console.log(`❌ Processing failed: ${result.error || 'Unknown error'}`);
      if (result.metadata?.errors) {
        console.log(`   Errors: ${result.metadata.errors.join(', ')}`);
      }
      return null;
    }
  } catch (error) {
    console.log(`❌ Processing error: ${error.message}`);
    return null;
  }
}

async function testCompleteWorkflow(filePath) {
  console.log(`\n🚀 Testing complete workflow for: ${path.basename(filePath)}`);
  console.log('=' .repeat(60));
  
  const startTime = Date.now();
  
  // Step 1: Upload
  const fileId = await testUploadEndpoint(filePath);
  if (!fileId) {
    console.log(`❌ Workflow failed at upload stage`);
    return false;
  }
  
  // Step 2: Process
  const result = await testProcessEndpoint(fileId, path.basename(filePath));
  if (!result) {
    console.log(`❌ Workflow failed at processing stage`);
    return false;
  }
  
  const totalTime = Date.now() - startTime;
  console.log(`\n✅ Complete workflow successful in ${totalTime}ms`);
  return true;
}

async function runAllTests() {
  console.log('🧪 Starting API Endpoint Tests');
  console.log('=' .repeat(60));
  console.log(`API Base URL: ${API_BASE}`);
  console.log(`Test Files: ${testFiles.length}`);
  
  let successCount = 0;
  let totalTests = testFiles.length;
  
  for (const filePath of testFiles) {
    const success = await testCompleteWorkflow(filePath);
    if (success) successCount++;
    
    // Wait between tests to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Test Summary');
  console.log(`✅ Successful: ${successCount}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - successCount}/${totalTests}`);
  
  if (successCount === totalTests) {
    console.log('🎉 All tests passed! The application is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Check if we're running in Node.js environment
if (typeof window === 'undefined') {
  console.log('⚠️  This script needs to run in a browser environment with fetch API');
  console.log('📋 Manual Testing Instructions:');
  console.log('1. Open http://localhost:5173 in your browser');
  console.log('2. Open browser developer console');
  console.log('3. Copy and paste this script');
  console.log('4. Call runAllTests() to execute the tests');
  console.log('\nOr test manually by:');
  console.log('- Dragging PDF files from examples/invoices/ to the upload area');
  console.log('- Watching the automatic processing');
  console.log('- Verifying the extracted data');
} else {
  // If running in browser, expose the test function
  window.runAllTests = runAllTests;
  window.testCompleteWorkflow = testCompleteWorkflow;
  console.log('🧪 Test functions loaded. Call runAllTests() to start testing.');
}
