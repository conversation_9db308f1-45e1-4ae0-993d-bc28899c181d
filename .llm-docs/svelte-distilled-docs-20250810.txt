## docs/svelte/01-introduction/01-overview.md

# Svelte 5 Reference

## Overview

Svelte is a compiler-based framework for building web UIs. It turns declarative components into optimized JavaScript.

```svelte
<!file: App.svelte>
<script>
	function greet() {
		alert('Welcome to Svelte!');
	}
</script>

<button onclick={greet}>click me</button>

<style>
	button {
		font-size: 2em;
	}
</style>
```

Components contain HTML, CSS, and JavaScript. Use with SvelteKit for full-stack apps.

## docs/svelte/01-introduction/02-getting-started.md

# Getting started

## Quick start with SvelteKit

Create a new project:

```sh
npx sv create myapp
cd myapp
npm install
npm run dev
```

## Alternatives

**Vite standalone**: `npm create vite@latest` → select `svelte` option. Generates HTML/JS/CSS in `dist/`. Need to add routing library.

**Other bundlers**: Rollup, Webpack plugins available, but Vite recommended.

## Editor tooling

- VS Code extension available
- Command line checking: `sv check`

## Getting help

- Discord chatroom
- Stack Overflow (svelte tag)

## docs/svelte/01-introduction/03-svelte-files.md

# .svelte files

Components are written in `.svelte` files using a superset of HTML. All sections are optional.

```svelte
/// file: MyComponent.svelte
<script module>
	// module-level logic goes here
	// (you will rarely use this)
</script>

<script>
	// instance-level logic goes here
</script>

<style>
	/* styles go here */
</style>
```

## `<script>`

Contains JavaScript/TypeScript (`lang="ts"`) that runs when component instance is created. Top-level variables can be referenced in markup.

Use runes for component props and reactivity.

## `<script module>`

Runs once when module first evaluates, not per component instance. Variables accessible elsewhere in component but not vice versa.

```svelte
<script module>
	let total = 0;
</script>

<script>
	total += 1;
	console.log(`instantiated ${total} times`);
</script>
```

Can `export` bindings (becomes module exports). Cannot `export default` - component is default export.

## `<style>`

CSS scoped to component only.

```svelte
<style>
	p {
		/* this will only affect <p> elements in this component */
		color: burlywood;
	}
</style>
```

## docs/svelte/01-introduction/04-svelte-js-files.md

# .svelte.js and .svelte.ts files

`.svelte.js` and `.svelte.ts` files behave like regular JS/TS modules but can use runes. Useful for reusable reactive logic and sharing reactive state across your app.

> Note: You cannot export reassigned state.

> New in Svelte 5

## docs/svelte/02-runes/01-what-are-runes.md

# Runes

Runes are symbols with `$` prefix that control the Svelte compiler. They're keywords, not functions.

```js
let message = $state('hello');
```

Key differences from functions:
- No import needed - they're globals
- Can't be assigned to variables or passed as arguments  
- Only valid in specific positions (compiler enforces this)

## docs/svelte/02-runes/02-$state.md

# $state

Creates reactive state that triggers UI updates when changed.

```svelte
<script>
	let count = $state(0);
</script>

<button onclick={() => count++}>
	clicks: {count}
</button>
```

State is just a regular variable - no special API needed for updates.

## Deep state

Arrays and simple objects become deeply reactive proxies:

```js
let todos = $state([
	{
		done: false,
		text: 'add more todos'
	}
]);
```

Individual property updates trigger granular reactivity:

```js
todos[0].done = !todos[0].done;
```

New objects pushed to arrays are automatically proxified:

```js
todos.push({
	done: false,
	text: 'eat lunch'
});
```

**Gotcha:** Destructuring breaks reactivity:

```js
let { done, text } = todos[0];
// `done` won't update when todos[0].done changes
todos[0].done = !todos[0].done;
```

## Classes

Use `$state` in class fields or constructor assignments:

```js
class Todo {
	done = $state(false);

	constructor(text) {
		this.text = $state(text);
	}

	reset() {
		this.text = '';
		this.done = false;
	}
}
```

**Gotcha:** Method binding loses `this` context:

```svelte
<!-- Won't work -->
<button onclick={todo.reset}>reset</button>

<!-- Use inline function -->
<button onclick={() => todo.reset()}>reset</button>
```

Or use arrow functions in class:

```js
class Todo {
	done = $state(false);
	
	reset = () => {
		this.text = '';
		this.done = false;
	}
}
```

## $state.raw

Non-reactive state for performance. Can only be reassigned, not mutated:

```js
let person = $state.raw({
	name: 'Heraclitus',
	age: 49
});

// No effect
person.age += 1;

// Works - full reassignment
person = {
	name: 'Heraclitus',
	age: 50
};
```

## $state.snapshot

Takes static snapshot of reactive state:

```svelte
<script>
	let counter = $state({ count: 0 });

	function onclick() {
		// Logs plain object, not proxy
		console.log($state.snapshot(counter));
	}
</script>
```

## Sharing state across modules

**Can't directly export reassignable state:**

```js
// ❌ Won't work
export let count = $state(0);
```

**Solutions:**

Export object with properties:
```js
export const counter = $state({
	count: 0
});
```

Or use getter functions:
```js
let count = $state(0);

export function getCount() {
	return count;
}

export function increment() {
	count += 1;
}
```

## docs/svelte/02-runes/03-$derived.md

# $derived

Derived state is declared with the `$derived` rune:

```svelte
<script>
	let count = $state(0);
	let doubled = $derived(count * 2);
</script>

<button onclick={() => count++}>
	{doubled}
</button>

<p>{count} doubled is {doubled}</p>
```

The expression inside `$derived(...)` should be free of side-effects. Svelte will disallow state changes (e.g. `count++`) inside derived expressions.

> **Note:** Code in Svelte components is only executed once at creation. Without the `$derived` rune, `doubled` would maintain its original value even when `count` changes.

## `$derived.by`

For complex derivations that don't fit in a short expression, use `$derived.by` with a function:

```svelte
<script>
	let numbers = $state([1, 2, 3]);
	let total = $derived.by(() => {
		let total = 0;
		for (const n of numbers) {
			total += n;
		}
		return total;
	});
</script>

<button onclick={() => numbers.push(numbers.length + 1)}>
	{numbers.join(' + ')} = {total}
</button>
```

`$derived(expression)` is equivalent to `$derived.by(() => expression)`.

## Dependencies

Anything read synchronously inside the `$derived` expression is a dependency. When dependencies change, the derived is recalculated when next read.

To exempt state from being a dependency, use `untrack`.

## Overriding derived values

You can temporarily override derived values by reassigning them (unless declared with `const`):

```svelte
<script>
	let { post, like } = $props();

	let likes = $derived(post.likes);

	async function onclick() {
		// increment the `likes` count immediately...
		likes += 1;

		// and tell the server, which will eventually update `post`
		try {
			await like();
		} catch {
			// failed! roll back the change
			likes -= 1;
		}
	}
</script>

<button {onclick}>🧡 {likes}</button>
```

## Deriveds and reactivity

Unlike `$state`, `$derived` values are left as-is (not converted to deeply reactive proxies):

```svelte
let items = $state([...]);

let index = $state(0);
let selected = $derived(items[index]);
```

You can change properties of `selected` and it will affect the underlying `items` array.

## Destructuring

Destructuring with `$derived` makes all resulting variables reactive:

```js
let { a, b, c } = $derived(stuff());
```

This is equivalent to:

```js
let _stuff = $derived(stuff());
let a = $derived(_stuff.a);
let b = $derived(_stuff.b);
let c = $derived(_stuff.c);
```

## Update propagation

Svelte uses push-pull reactivity - when state updates, dependents are notified immediately (push), but derived values aren't re-evaluated until read (pull).

If a derived's new value is referentially identical to its previous value, downstream updates are skipped:

```svelte
<script>
	let count = $state(0);
	let large = $derived(count > 10);
</script>

<button onclick={() => count++}>
	{large}
</button>
```

The button only updates when `large` changes, not when `count` changes.

## docs/svelte/02-runes/04-$effect.md

# $effect

Effects run when state updates. Used for third-party libraries, canvas drawing, network requests. Only run in browser, not SSR.

**Don't update state inside effects** - leads to infinite loops. Use alternatives below.

## Basic Usage

```svelte
<script>
	let size = $state(50);
	let color = $state('#ff3e00');

	let canvas;

	$effect(() => {
		const context = canvas.getContext('2d');
		context.clearRect(0, 0, canvas.width, canvas.height);

		// this will re-run whenever `color` or `size` change
		context.fillStyle = color;
		context.fillRect(0, 0, size, size);
	});
</script>

<canvas bind:this={canvas} width="100" height="100"></canvas>
```

## Lifecycle & Teardown

Effects run after mount in microtasks. Re-runs are batched. Can return teardown function:

```svelte
<script>
	let count = $state(0);
	let milliseconds = $state(1000);

	$effect(() => {
		// This will be recreated whenever `milliseconds` changes
		const interval = setInterval(() => {
			count += 1;
		}, milliseconds);

		return () => {
			// if a teardown function is provided, it will run
			// a) immediately before the effect re-runs
			// b) when the component is destroyed
			clearInterval(interval);
		};
	});
</script>

<h1>{count}</h1>

<button onclick={() => (milliseconds *= 2)}>slower</button>
<button onclick={() => (milliseconds /= 2)}>faster</button>
```

## Dependencies

Auto-tracks reactive values read **synchronously**. Async reads (after `await`, inside `setTimeout`) not tracked:

```ts
$effect(() => {
	const context = canvas.getContext('2d');
	context.clearRect(0, 0, canvas.width, canvas.height);

	// this will re-run whenever `color` changes...
	context.fillStyle = color;

	setTimeout(() => {
		// ...but not when `size` changes
		context.fillRect(0, 0, size, size);
	}, 0);
});
```

**Object vs property tracking:**

```svelte
<script>
	let state = $state({ value: 0 });
	let derived = $derived({ value: state.value * 2 });

	// this will run once, because `state` is never reassigned (only mutated)
	$effect(() => {
		state;
	});

	// this will run whenever `state.value` changes...
	$effect(() => {
		state.value;
	});

	// ...and so will this, because `derived` is a new object each time
	$effect(() => {
		derived;
	});
</script>

<button onclick={() => (state.value += 1)}>
	{state.value}
</button>

<p>{state.value} doubled is {derived.value}</p>
```

**Conditional dependencies:**

```ts
import confetti from 'canvas-confetti';

let condition = $state(true);
let color = $state('#ff3e00');

$effect(() => {
	if (condition) {
		confetti({ colors: [color] });
	} else {
		confetti();
	}
});
```

## $effect.pre

Runs **before** DOM updates:

```svelte
<script>
	import { tick } from 'svelte';

	let div = $state();
	let messages = $state([]);

	// ...

	$effect.pre(() => {
		if (!div) return; // not yet mounted

		// reference `messages` array length so that this code re-runs whenever it changes
		messages.length;

		// autoscroll when new messages are added
		if (div.offsetHeight + div.scrollTop > div.scrollHeight - 20) {
			tick().then(() => {
				div.scrollTo(0, div.scrollHeight);
			});
		}
	});
</script>

<div bind:this={div}>
	{#each messages as message}
		<p>{message}</p>
	{/each}
</div>
```

## $effect.tracking

Returns if code runs inside tracking context:

```svelte
<script>
	console.log('in component setup:', $effect.tracking()); // false

	$effect(() => {
		console.log('in effect:', $effect.tracking()); // true
	});
</script>

<p>in template: {$effect.tracking()}</p> 
```

## $effect.pending

Returns number of pending promises in current boundary:

```svelte
<button onclick={() => a++}>a++</button>
<button onclick={() => b++}>b++</button>

<p>{a} + {b} = {await add(a, b)}</p>

{#if $effect.pending()}
	<p>pending promises: {$effect.pending()}</p>
{/if}
```

## $effect.root

Creates non-tracked scope for manual control:

```js
const destroy = $effect.root(() => {
	$effect(() => {
		// setup
	});

	return () => {
		// cleanup
	};
});

// later...
destroy();
```

## When NOT to use $effect

**❌ Don't synchronize state:**

```svelte
<script>
	let count = $state(0);
	let doubled = $state();

	// don't do this!
	$effect(() => {
		doubled = count * 2;
	});
</script>
```

**✅ Use $derived instead:**

```svelte
<script>
	let count = $state(0);
	let doubled = $derived(count * 2);
</script>
```

**❌ Don't link values with effects:**

```svelte
<script>
	const total = 100;
	let spent = $state(0);
	let left = $state(total);

	$effect(() => {
		left = total - spent;
	});

	$effect(() => {
		spent = total - left;
	});
</script>

<label>
	<input type="range" bind:value={spent} max={total} />
	{spent}/{total} spent
</label>

<label>
	<input type="range" bind:value={left} max={total} />
	{left}/{total} left
</label>
```

**✅ Use function bindings:**

```svelte
<script>
	const total = 100;
	let spent = $state(0);
	let left = $derived(total - spent);

function updateLeft(left) {
		spent = total - left;
	}
</script>

<label>
	<input type="range" bind:value={spent} max={total} />
	{spent}/{total} spent
</label>

<label>
	<input type="range"bind:value={() => left, updateLeft}max={total} />
	{left}/{total} left
</label>
```

## docs/svelte/02-runes/05-$props.md

# $props

Pass props to components like attributes:

```svelte
<!file: App.svelte>
<script>
	import MyComponent from './MyComponent.svelte';
</script>

<MyComponent adjective="cool" />
```

Receive props with `$props` rune:

```svelte
<!file: MyComponent.svelte>
<script>
	let props = $props();
</script>

<p>this component is {props.adjective}</p>
```

More commonly, destructure props:

```svelte
<!file: MyComponent.svelte>
<script>
	let{ adjective }= $props();
</script>

<p>this component is {adjective}</p>
```

## Fallback values

```js
let { adjective = 'happy' } = $props();
```

> Fallback values are not turned into reactive state proxies

## Renaming props

```js
let { super: trouper = 'lights are gonna find me' } = $props();
```

## Rest props

```js
let { a, b, c, ...others } = $props();
```

## Updating props

Props update when parent changes. Child can temporarily override prop values:

```svelte
<!file: App.svelte>
<script>
	import Child from './Child.svelte';

	let count = $state(0);
</script>

<button onclick={() => (count += 1)}>
	clicks (parent): {count}
</button>

<Child {count} />
```

```svelte
<!file: Child.svelte>
<script>
	let { count } = $props();
</script>

<button onclick={() => (count += 1)}>
	clicks (child): {count}
</button>
```

**Don't mutate props** unless they are `$bindable`. Mutating regular objects has no effect. Mutating reactive state proxies causes `ownership_invalid_mutation` warnings.

## Type safety

TypeScript:
```svelte
<script lang="ts">
	let { adjective }: { adjective: string } = $props();
</script>
```

JSDoc:
```svelte
<script>
	/** @type {{ adjective: string }} */
	let { adjective } = $props();
</script>
```

Interface:
```svelte
<script lang="ts">
	interface Props {
		adjective: string;
	}

	let { adjective }: Props = $props();
</script>
```

## `$props.id()`

Generates unique ID per component instance, consistent between server/client:

```svelte
<script>
	const uid = $props.id();
</script>

<form>
	<label for="{uid}-firstname">First Name: </label>
	<input id="{uid}-firstname" type="text" />

	<label for="{uid}-lastname">Last Name: </label>
	<input id="{uid}-lastname" type="text" />
</form>
```

## docs/svelte/02-runes/06-$bindable.md

# $bindable

Props normally flow one-way from parent to child. `$bindable` allows two-way data flow and state mutation in the child component.

## Basic Usage

Mark a prop as bindable:

```svelte
/// file: FancyInput.svelte
<script>
	let { value = $bindable(), ...props } = $props();
</script>

<input bind:value={value} {...props} />

<style>
	input {
		font-family: 'Comic Sans MS';
		color: deeppink;
	}
</style>
```

Use with `bind:` directive in parent:

```svelte
/// file: App.svelte
<script>
	import FancyInput from './FancyInput.svelte';

	let message = $state('hello');
</script>

<FancyInput bind:value={message} />
<p>{message}</p>
```

## Fallback Values

Provide fallback when no prop is passed:

```js
/// file: FancyInput.svelte
let { value = $bindable('fallback'), ...props } = $props();
```

**Note:** Parent components can pass normal props instead of using `bind:` - binding is optional.

## docs/svelte/02-runes/07-$inspect.md

# $inspect

> [!NOTE] `$inspect` only works during development. In a production build it becomes a noop.

The `$inspect` rune is equivalent to `console.log` but re-runs when its arguments change. Tracks reactive state deeply.

```svelte
<script>
	let count = $state(0);
	let message = $state('hello');

	$inspect(count, message); // will console.log when `count` or `message` change
</script>

<button onclick={() => count++}>Increment</button>
<input bind:value={message} />
```

## $inspect(...).with

Returns a `with` property to invoke a custom callback instead of `console.log`. First argument is `"init"` or `"update"`.

```svelte
<script>
	let count = $state(0);

	$inspect(count).with((type, count) => {
		if (type === 'update') {
			debugger; // or `console.trace`, or whatever you want
		}
	});
</script>

<button onclick={() => count++}>Increment</button>
```

Find change origins with `console.trace`:

```js
// @errors: 2304
$inspect(stuff).with(console.trace);
```

## $inspect.trace(...)

Traces function re-runs in development. Shows which reactive state caused effects/derived to fire. Must be first statement in function body.

```svelte
<script>
	import { doSomeWork } from './elsewhere';

	$effect(() => {
		// $inspect.trace must be the first statement of a function body
		$inspect.trace();
		doSomeWork();
	});
</script>
```

Takes optional label as first argument.

## docs/svelte/02-runes/08-$host.md

# $host

The `$host` rune provides access to the host element when compiling a component as a custom element. Commonly used to dispatch custom events.

```svelte
/// file: Stepper.svelte
<svelte:options customElement="my-stepper" />

<script>
	function dispatch(type) {
		$host().dispatchEvent(new CustomEvent(type));
	}
</script>

<button onclick={() => dispatch('decrement')}>decrement</button>
<button onclick={() => dispatch('increment')}>increment</button>
```

```svelte
/// file: App.svelte
<script>
	import './Stepper.svelte';

	let count = $state(0);
</script>

<my-stepper
	ondecrement={() => count -= 1}
	onincrement={() => count += 1}
></my-stepper>

<p>count: {count}</p>
```

## docs/svelte/03-template-syntax/01-basic-markup.md

# Basic markup

Markup inside Svelte components is HTML++.

## Tags

- Lowercase tags (`<div>`) = HTML elements
- Capitalized/dot notation (`<Widget>`, `<my.stuff>`) = components

```svelte
<script>
	import Widget from './Widget.svelte';
</script>

<div>
	<Widget />
</div>
```

## Element attributes

Work like HTML. Values can contain or be JavaScript expressions:

```svelte
<div class="foo">
	<button disabled>can't touch this</button>
</div>
```

```svelte
<input type=checkbox />
```

```svelte
<a href="page/{p}">page {p}</a>
```

```svelte
<button disabled={!clickable}>...</button>
```

**Attribute rules:**
- Boolean attributes: included if truthy, excluded if falsy
- Other attributes: included unless nullish (`null`/`undefined`)

```svelte
<input required={false} placeholder="This input field is not required" />
<div title={null}>This div has no title attribute</div>
```

**Shorthand:** `name={name}` → `{name}`

```svelte
<button {disabled}>...</button>
```

## Component props

Same rules as attributes. Use `{name}` shorthand when possible:

```svelte
<Widget foo={bar} answer={42} text="hello" />
```

## Spread attributes

Pass multiple attributes at once. Order matters:

```svelte
<Widget a="b" {...things} c="d" />
```

## Events

Add `on` prefix to event name. Case sensitive:

```svelte
<button onclick={() => console.log('clicked')}>click me</button>
```

- `onclick` = `click` event
- `onClick` = `Click` event (different)
- Support shorthand: `<button {onclick}>`
- Support spread: `<button {...eventAttrs}>`
- Fire after binding events

**Performance:** `ontouchstart`/`ontouchmove` are passive by default.

### Event delegation

Svelte delegates these events to app root for performance:
`beforeinput`, `click`, `change`, `dblclick`, `contextmenu`, `focusin`, `focusout`, `input`, `keydown`, `keyup`, `mousedown`, `mousemove`, `mouseout`, `mouseover`, `mouseup`, `pointerdown`, `pointermove`, `pointerout`, `pointerover`, `pointerup`, `touchend`, `touchmove`, `touchstart`

**Gotchas:**
- Manual dispatch needs `{ bubbles: true }`
- Avoid `stopPropagation` with `addEventListener`
- Use `on` from `svelte/events` instead of `addEventListener`

## Text expressions

JavaScript in curly braces. `null`/`undefined` omitted, others stringified:

```svelte
<h1>Hello {name}!</h1>
<p>{a} + {b} = {a + b}.</p>

<div>{(/^[A-Za-z ]+$/).test(value) ? x : y}</div>
```

**HTML rendering:** Use `{@html}` (escape to prevent XSS):

```svelte
{@html potentiallyUnsafeHtmlString}
```

## Comments

HTML comments work. `svelte-ignore` disables warnings:

```svelte
<h1>Hello world</h1>
```

```svelte

<input bind:value={name} autofocus />
```

`@component` comments show on hover:

````svelte

<script>
	let { name } = $props();
</script>

<main>
	<h1>
		Hello, {name}
	</h1>
</main>
````

## docs/svelte/03-template-syntax/02-if.md

# {#if ...}

Conditionally render content using if blocks.

## Syntax

```svelte
{#if expression}...{/if}
{#if expression}...{:else if expression}...{/if}
{#if expression}...{:else}...{/if}
```

## Examples

Basic conditional:
```svelte
{#if answer === 42}
	<p>what was the question?</p>
{/if}
```

Multiple conditions:
```svelte
{#if porridge.temperature > 100}
	<p>too hot!</p>
{:else if 80 > porridge.temperature}
	<p>too cold!</p>
{:else}
	<p>just right!</p>
{/if}
```

Blocks can wrap elements or text within elements.

## docs/svelte/03-template-syntax/03-each.md

# {#each ...}

Iterate over arrays, array-like objects (with `length` property), or iterables like `Map` and `Set`.

## Basic syntax

```svelte
{#each expression as name}...{/each}
{#each expression as name, index}...{/each}
```

```svelte
<h1>Shopping list</h1>
<ul>
	{#each items as item}
		<li>{item.name} x {item.qty}</li>
	{/each}
</ul>
```

With index:
```svelte
{#each items as item, i}
	<li>{i + 1}: {item.name} x {item.qty}</li>
{/each}
```

## Keyed each blocks

Use keys for efficient updates when data changes - Svelte will intelligently insert/move/delete items instead of updating in place.

```svelte
{#each expression as name (key)}...{/each}
{#each expression as name, index (key)}...{/each}
```

```svelte
{#each items as item (item.id)}
	<li>{item.name} x {item.qty}</li>
{/each}

{#each items as item, i (item.id)}
	<li>{i + 1}: {item.name} x {item.qty}</li>
{/each}
```

Keys should be strings or numbers for identity persistence.

## Destructuring

```svelte
{#each items as { id, name, qty }, i (id)}
	<li>{i + 1}: {name} x {qty}</li>
{/each}

{#each objects as { id, ...rest }}
	<li><span>{id}</span><MyComponent {...rest} /></li>
{/each}

{#each items as [id, ...rest]}
	<li><span>{id}</span><MyComponent values={rest} /></li>
{/each}
```

## Without item binding

Render something `n` times by omitting the `as` part:

```svelte
{#each expression}...{/each}
{#each expression, index}...{/each}
```

```svelte
<div class="chess-board">
	{#each { length: 8 }, rank}
		{#each { length: 8 }, file}
			<div class:black={(rank + file) % 2 === 1}></div>
		{/each}
	{/each}
</div>
```

## Else blocks

Rendered when list is empty:

```svelte
{#each todos as todo}
	<p>{todo.text}</p>
{:else}
	<p>No tasks today!</p>
{/each}
```

## docs/svelte/03-template-syntax/04-key.md

# {#key ...}

```svelte
{#key expression}...{/key}
```

Destroys and recreates contents when expression changes.

**Component reinstantiation:**
```svelte
{#key value}
	<Component />
{/key}
```

**Trigger transitions on value change:**
```svelte
{#key value}
	<div transition:fade>{value}</div>
{/key}
```

## docs/svelte/03-template-syntax/05-await.md

# {#await ...}

Handle Promise states with await blocks:

```svelte
{#await expression}...{:then name}...{:catch name}...{/await}
{#await expression}...{:then name}...{/await}
{#await expression then name}...{/await}
{#await expression catch name}...{/await}
```

## Basic Usage

```svelte
{#await promise}
	<p>waiting for the promise to resolve...</p>
{:then value}
	<p>The value is {value}</p>
{:catch error}
	<p>Something went wrong: {error.message}</p>
{/await}
```

## Omit Blocks

Skip catch block:
```svelte
{#await promise}
	<p>waiting for the promise to resolve...</p>
{:then value}
	<p>The value is {value}</p>
{/await}
```

Skip pending state:
```svelte
{#await promise then value}
	<p>The value is {value}</p>
{/await}
```

Error only:
```svelte
{#await promise catch error}
	<p>The error is {error}</p>
{/await}
```

## Lazy Component Loading

```svelte
{#await import('./Component.svelte') then { default: Component }}
	<Component />
{/await}
```

**Note:** During SSR, only pending branch renders. If expression isn't a Promise, only `:then` branch renders.

## docs/svelte/03-template-syntax/06-snippet.md

# {#snippet ...}

Create reusable chunks of markup inside components.

```svelte
{#snippet name()}...{/snippet}
{#snippet name(param1, param2, paramN)}...{/snippet}
```

## Basic Usage

Instead of duplicating markup:

```svelte
{#each images as image}
	{#if image.href}
		<a href={image.href}>
			<figure>
				<img src={image.src} alt={image.caption} width={image.width} height={image.height} />
				<figcaption>{image.caption}</figcaption>
			</figure>
		</a>
	{:else}
		<figure>
			<img src={image.src} alt={image.caption} width={image.width} height={image.height} />
			<figcaption>{image.caption}</figcaption>
		</figure>
	{/if}
{/each}
```

Use snippets:

```svelte
{#snippet figure(image)}
	<figure>
		<img src={image.src} alt={image.caption} width={image.width} height={image.height} />
		<figcaption>{image.caption}</figcaption>
	</figure>
{/snippet}

{#each images as image}
	{#if image.href}
		<a href={image.href}>
			{@render figure(image)}
		</a>
	{:else}
		{@render figure(image)}
	{/if}
{/each}
```

## Snippet Scope

Snippets can reference values from `<script>` or blocks:

```svelte
<script>
	let { message = `it's great to see you!` } = $props();
</script>

{#snippet hello(name)}
	<p>hello {name}! {message}!</p>
{/snippet}

{@render hello('alice')}
{@render hello('bob')}
```

Snippets can reference themselves and each other:

```svelte
{#snippet blastoff()}
	<span>🚀</span>
{/snippet}

{#snippet countdown(n)}
	{#if n > 0}
		<span>{n}...</span>
		{@render countdown(n - 1)}
	{:else}
		{@render blastoff()}
	{/if}
{/snippet}

{@render countdown(10)}
```

## Passing Snippets to Components

### Explicit Props

```svelte
<script>
	import Table from './Table.svelte';

	const fruits = [
		{ name: 'apples', qty: 5, price: 2 },
		{ name: 'bananas', qty: 10, price: 1 },
		{ name: 'cherries', qty: 20, price: 0.5 }
	];
</script>

{#snippet header()}
	<th>fruit</th>
	<th>qty</th>
	<th>price</th>
	<th>total</th>
{/snippet}

{#snippet row(d)}
	<td>{d.name}</td>
	<td>{d.qty}</td>
	<td>{d.price}</td>
	<td>{d.qty * d.price}</td>
{/snippet}

<Table data={fruits} {header} {row} />
```

### Implicit Props

Snippets declared inside a component become props:

```svelte
<Table data={fruits}>
	{#snippet header()}
		<th>fruit</th>
		<th>qty</th>
		<th>price</th>
		<th>total</th>
	{/snippet}

	{#snippet row(d)}
		<td>{d.name}</td>
		<td>{d.qty}</td>
		<td>{d.price}</td>
		<td>{d.qty * d.price}</td>
	{/snippet}
</Table>
```

### Implicit `children` Snippet

Content inside component tags becomes the `children` snippet:

```svelte
<!-- App.svelte -->
<Button>click me</Button>
```

```svelte
<!-- Button.svelte -->
<script>
	let { children } = $props();
</script>

<button>{@render children()}</button>
```

### Optional Snippets

Use optional chaining:

```svelte
<script>
    let { children } = $props();
</script>

{@render children?.()}
```

Or `#if` with fallback:

```svelte
<script>
    let { children } = $props();
</script>

{#if children}
    {@render children()}
{:else}
    fallback content
{/if}
```

## TypeScript

Use the `Snippet` interface:

```svelte
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		data: any[];
		children: Snippet;
		row: Snippet<[any]>;
	}

	let { data, children, row }: Props = $props();
</script>
```

With generics:

```svelte
<script lang="ts" generics="T">
	import type { Snippet } from 'svelte';

	let {
		data,
		children,
		row
	}: {
		data: T[];
		children: Snippet;
		row: Snippet<[T]>;
	} = $props();
</script>
```

## Exporting Snippets

Export from `<script module>` (Svelte 5.5.0+):

```svelte
<script module>
	export { add };
</script>

{#snippet add(a, b)}
	{a} + {b} = {a + b}
{/snippet}
```

## Programmatic Snippets

Use [`createRawSnippet`](svelte#createRawSnippet) API for advanced use cases.

> **Note:** Snippets replace deprecated slots from Svelte 4.

## docs/svelte/03-template-syntax/<EMAIL>

# {@render ...}

Render [snippets](snippet) using `{@render ...}` tags.

```svelte
{#snippet sum(a, b)}
	<p>{a} + {b} = {a + b}</p>
{/snippet}

{@render sum(1, 2)}
{@render sum(3, 4)}
{@render sum(5, 6)}
```

Expression can be identifier or JavaScript expression:

```svelte
{@render (cool ? coolSnippet : lameSnippet)()}
```

## Optional snippets

Use optional chaining for potentially undefined snippets:

```svelte
{@render children?.()}
```

Or `{#if}` with fallback:

```svelte
{#if children}
	{@render children()}
{:else}
	<p>fallback content</p>
{/if}
```

## docs/svelte/03-template-syntax/<EMAIL>

# {@html ...}

Inject raw HTML into components:

```svelte
<article>
	{@html content}
</article>
```

**Security**: Always escape or control content to prevent XSS attacks. Never render unsanitized content.

**Requirements**: 
- Expression must be valid standalone HTML
- Cannot split HTML tags across multiple `{@html}` blocks
- Does not compile Svelte code

## Styling

`{@html}` content doesn't receive scoped styles. Use `:global` modifier:

```svelte
<style>
	article:global{
		a { color: hotpink }
		img { width: 100% }
	}
</style>
```

## docs/svelte/03-template-syntax/<EMAIL>

# {@attach ...}

Attachments are functions that run in an effect when an element mounts or when state updates. They can return a cleanup function.

```svelte
<script>
	/** @type {import('svelte/attachments').Attachment} */
	function myAttachment(element) {
		console.log(element.nodeName); // 'DIV'

		return () => {
			console.log('cleaning up');
		};
	}
</script>

<div {@attach myAttachment}>...</div>
```

## Attachment factories

Functions can return attachments:

```svelte
<script>
	import tippy from 'tippy.js';

	let content = $state('Hello!');

	/**
	 * @param {string} content
	 * @returns {import('svelte/attachments').Attachment}
	 */
	function tooltip(content) {
		return (element) => {
			const tooltip = tippy(element, { content });
			return tooltip.destroy;
		};
	}
</script>

<input bind:value={content} />

<button {@attach tooltip(content)}>
	Hover me
</button>
```

Attachment recreates when `content` changes.

## Inline attachments

```svelte
<canvas
	width={32}
	height={32}
	{@attach (canvas) => {
		const context = canvas.getContext('2d');

		$effect(() => {
			context.fillStyle = color;
			context.fillRect(0, 0, canvas.width, canvas.height);
		});
	}}
></canvas>
```

## Passing to components

Attachments create Symbol props that spread to elements:

```svelte
<!file: Button.svelte>
<script>
	/** @type {import('svelte/elements').HTMLButtonAttributes} */
	let { children, ...props } = $props();
</script>

<button {...props}>
	{@render children?.()}
</button>
```

```svelte
<!file: App.svelte>
<script>
	import tippy from 'tippy.js';
	import Button from './Button.svelte';

	let content = $state('Hello!');

	/**
	 * @param {string} content
	 * @returns {import('svelte/attachments').Attachment}
	 */
	function tooltip(content) {
		return (element) => {
			const tooltip = tippy(element, { content });
			return tooltip.destroy;
		};
	}
</script>

<input bind:value={content} />

<Button {@attach tooltip(content)}>
	Hover me
</Button>
```

## Controlling re-runs

Attachments are fully reactive. To avoid expensive re-runs, pass data in a function:

```js
function foo(getBar) {
	return (node) => {
		veryExpensiveSetupWork(node);

		$effect(() => {
			update(node, getBar());
		});
	}
}
```

## Programmatic creation

Use [`createAttachmentKey`](svelte-attachments#createAttachmentKey) to add attachments to spread objects.

Convert actions to attachments with [`fromAction`](svelte-attachments#fromAction).

## docs/svelte/03-template-syntax/<EMAIL>

# {@const ...}

The `{@const ...}` tag defines a local constant within blocks.

```svelte
{#each boxes as box}
	{@const area = box.width * box.height}
	{box.width} * {box.height} = {area}
{/each}
```

**Usage**: Only allowed as immediate child of blocks (`{#if}`, `{#each}`, `{#snippet}`), components (`<Component />`), or `<svelte:boundary>`.

## docs/svelte/03-template-syntax/<EMAIL>

# {@debug ...}

The `{@debug ...}` tag logs variable values when they change and pauses execution if devtools are open.

```svelte
<script>
	let user = {
		firstname: 'Ada',
		lastname: 'Lovelace'
	};
</script>

{@debug user}

<h1>Hello {user.firstname}!</h1>
```

## Usage

Accepts comma-separated variable names (not arbitrary expressions):

```svelte
{@debug user}
{@debug user1, user2, user3}

<!-- Invalid -->
{@debug user.firstname}
{@debug myArray[0]}
{@debug !isReady}
{@debug typeof user === 'object'}
```

Empty `{@debug}` triggers on any state change:

```svelte
{@debug}
```

## docs/svelte/03-template-syntax/12-bind.md

# bind:

Data flows down from parent to child. `bind:` allows data to flow from child to parent.

Syntax: `bind:property={expression}` or `bind:property` (shorthand when names match).

```svelte
<input bind:value={value} />
<input bind:value />
```

Svelte creates event listeners that update bound values. Most bindings are two-way, some are readonly.

## Function bindings

Use `bind:property={get, set}` for validation/transformation:

```svelte
<input bind:value={
	() => value,
	(v) => value = v.toLowerCase()}
/>
```

For readonly bindings, set `get` to `null`:

```svelte
<div
	bind:clientWidth={null, redraw}
	bind:clientHeight={null, redraw}
>...</div>
```

## Input bindings

### `bind:value`

```svelte
<script>
	let message = $state('hello');
</script>

<input bind:value={message} />
<p>{message}</p>
```

Numeric inputs coerce to numbers:

```svelte
<script>
	let a = $state(1);
	let b = $state(2);
</script>

<label>
	<input type="number" bind:value={a} min="0" max="10" />
	<input type="range" bind:value={a} min="0" max="10" />
</label>

<label>
	<input type="number" bind:value={b} min="0" max="10" />
	<input type="range" bind:value={b} min="0" max="10" />
</label>

<p>{a} + {b} = {a + b}</p>
```

Empty/invalid numeric inputs return `undefined`.

Form reset with `defaultValue`:

```svelte
<script>
	let value = $state('');
</script>

<form>
	<input bind:value defaultValue="not the empty string">
	<input type="reset" value="Reset">
</form>
```

### `bind:checked`

```svelte
<label>
	<input type="checkbox" bind:checked={accepted} />
	Accept terms and conditions
</label>
```

Form reset with `defaultChecked`:

```svelte
<script>
	let checked = $state(true);
</script>

<form>
	<input type="checkbox" bind:checked defaultChecked={true}>
	<input type="reset" value="Reset">
</form>
```

### `bind:indeterminate`

```svelte
<script>
	let checked = $state(false);
	let indeterminate = $state(true);
</script>

<form>
	<input type="checkbox" bind:checked bind:indeterminate>

	{#if indeterminate}
		waiting...
	{:else if checked}
		checked
	{:else}
		unchecked
	{/if}
</form>
```

### `bind:group`

```svelte
<script>
	let tortilla = $state('Plain');

	/** @type {string[]} */
	let fillings = $state([]);
</script>


<label><input type="radio" bind:group={tortilla} value="Plain" /> Plain</label>
<label><input type="radio" bind:group={tortilla} value="Whole wheat" /> Whole wheat</label>
<label><input type="radio" bind:group={tortilla} value="Spinach" /> Spinach</label>


<label><input type="checkbox" bind:group={fillings} value="Rice" /> Rice</label>
<label><input type="checkbox" bind:group={fillings} value="Beans" /> Beans</label>
<label><input type="checkbox" bind:group={fillings} value="Cheese" /> Cheese</label>
<label><input type="checkbox" bind:group={fillings} value="Guac (extra)" /> Guac (extra)</label>
```

> Only works within same component.

### `bind:files`

```svelte
<script>
	let files = $state();

	function clear() {
		files = new DataTransfer().files; // null or undefined does not work
	}
</script>

<label for="avatar">Upload a picture:</label>
<input accept="image/png, image/jpeg" bind:files id="avatar" name="avatar" type="file" />
<button onclick={clear}>clear</button>
```

Use `DataTransfer` to create/modify `FileList` objects.

## Select bindings

Single select:

```svelte
<select bind:value={selected}>
	<option value={a}>a</option>
	<option value={b}>b</option>
	<option value={c}>c</option>
</select>
```

Multiple select:

```svelte
<select multiple bind:value={fillings}>
	<option value="Rice">Rice</option>
	<option value="Beans">Beans</option>
	<option value="Cheese">Cheese</option>
	<option value="Guac (extra)">Guac (extra)</option>
</select>
```

Default selection with `selected` attribute:

```svelte
<select bind:value={selected}>
	<option value={a}>a</option>
	<option value={b} selected>b</option>
	<option value={c}>c</option>
</select>
```

## Media bindings

### `<audio>`

Two-way: `currentTime`, `playbackRate`, `paused`, `volume`, `muted`
Readonly: `duration`, `buffered`, `seekable`, `seeking`, `ended`, `readyState`, `played`

```svelte
<audio src={clip} bind:duration bind:currentTime bind:paused></audio>
```

### `<video>`

Same as audio plus readonly `videoWidth` and `videoHeight`.

### `<img>`

Readonly: `naturalWidth`, `naturalHeight`

## Other bindings

### `<details>`

```svelte
<details bind:open={isOpen}>
	<summary>How do you comfort a JavaScript bug?</summary>
	<p>You console it.</p>
</details>
```

### Contenteditable

Bindings: `innerHTML`, `innerText`, `textContent`

```svelte
<div contenteditable="true" bind:innerHTML={html}></div>
```

### Dimensions

Readonly bindings using `ResizeObserver`: `clientWidth`, `clientHeight`, `offsetWidth`, `offsetHeight`, `contentRect`, `contentBoxSize`, `borderBoxSize`, `devicePixelContentBoxSize`

```svelte
<div bind:offsetWidth={width} bind:offsetHeight={height}>
	<Chart {width} {height} />
</div>
```

> `display: inline` elements need `display: inline-block` or similar.

## bind:this

Get DOM node reference:

```svelte
<script>
	/** @type {HTMLCanvasElement} */
	let canvas;

	$effect(() => {
		const ctx = canvas.getContext('2d');
		drawStuff(ctx);
	});
</script>

<canvas bind:this={canvas}></canvas>
```

Component instance reference:

```svelte
<!-- App.svelte -->
<ShoppingCart bind:this={cart} />

<button onclick={() => cart.empty()}> Empty shopping cart </button>
```

```svelte
<!-- ShoppingCart.svelte -->
<script>
	// All instance exports are available on the instance object
	export function empty() {
		// ...
	}
</script>
```

## Component prop binding

```svelte
<Keypad bind:value={pin} />
```

Mark props as bindable with `$bindable()`:

```svelte
<script>
	let { readonlyProperty, bindableProperty = $bindable() } = $props();
</script>
```

With fallback value:

```svelte
<script>
	let { bindableProperty = $bindable('fallback value') } = $props();
</script>
```

Fallback only applies when not bound. When bound with fallback, parent must provide non-`undefined` value.

## docs/svelte/03-template-syntax/13-use.md

# use:

> [!NOTE]
> In Svelte 5.29+, consider using [attachments](@attach) instead - more flexible and composable.

Actions are functions called when an element is mounted. Use `use:` directive with `$effect` for cleanup:

```svelte
<!file: App.svelte>
<script>
	/** @type {import('svelte/action').Action} */
	function myaction(node) {
		// node mounted in DOM

		$effect(() => {
			// setup goes here

			return () => {
				// teardown goes here
			};
		});
	}
</script>

<div use:myaction>...</div>
```

## With Arguments

```svelte
<!file: App.svelte>
<script>
	/** @type {import('svelte/action').Action} */
	function myaction(node,data) {
		// ...
	}
</script>

<div use:myaction={data}>...</div>
```

**Key:** Action called once only (not on SSR). Won't re-run if argument changes.

## Typing

`Action` interface: `Action<NodeType, Parameter, CustomEvents>`

```svelte
<!file: App.svelte>
<script>
	/**
	 * @type {import('svelte/action').Action<
	 * 	HTMLDivElement,
	 * 	undefined,
	 * 	{
	 * 		onswiperight: (e: CustomEvent) => void;
	 * 		onswipeleft: (e: CustomEvent) => void;
	 * 		// ...
	 * 	}
	 * >}
	 */
	function gestures(node) {
		$effect(() => {
			// ...
			node.dispatchEvent(new CustomEvent('swipeleft'));

			// ...
			node.dispatchEvent(new CustomEvent('swiperight'));
		});
	}
</script>

<div
	use:gestures
	onswipeleft={next}
	onswiperight={prev}
>...</div>
```

## docs/svelte/03-template-syntax/14-transition.md

# transition:

Transitions trigger when elements enter/leave DOM due to state changes. All elements in a transitioning block stay in DOM until all transitions complete.

`transition:` creates bidirectional transitions that can reverse smoothly while in progress.

```svelte
<script>
	import { fade } from 'svelte/transition';

	let visible = $state(false);
</script>

<button onclick={() => visible = !visible}>toggle</button>

{#if visible}
	<divtransition:fade>fades in and out</div>
{/if}
```

## Local vs Global

Local (default): only play when their block is created/destroyed
Global: play when any parent block changes

```svelte
{#if x}
	{#if y}
		<p transition:fade>fades in and out only when y changes</p>

		<p transition:fade|global>fades in and out when x or y change</p>
	{/if}
{/if}
```

## Built-in Transitions

Import from `svelte/transition` module.

## Parameters

Pass parameters using object literal:

```svelte
{#if visible}
	<div transition:fade={{ duration: 2000 }}>fades in and out over two seconds</div>
{/if}
```

## Custom Functions

```js
transition = (node: HTMLElement, params: any, options: { direction: 'in' | 'out' | 'both' }) => {
	delay?: number,
	duration?: number,
	easing?: (t: number) => number,
	css?: (t: number, u: number) => string,
	tick?: (t: number, u: number) => void
}
```

`t`: 0-1 after easing (1 = natural state)
`u`: 1-t

Use `css` for web animations (runs off main thread):

```svelte
<!file: App.svelte>
<script>
	import { elasticOut } from 'svelte/easing';

	/** @type {boolean} */
	export let visible;

	/**
	 * @param {HTMLElement} node
	 * @param {{ delay?: number, duration?: number, easing?: (t: number) => number }} params
	 */
	function whoosh(node, params) {
		const existingTransform = getComputedStyle(node).transform.replace('none', '');

		return {
			delay: params.delay || 0,
			duration: params.duration || 400,
			easing: params.easing || elasticOut,
			css: (t, u) => `transform: ${existingTransform} scale(${t})`
		};
	}
</script>

{#if visible}
	<div in:whoosh>whooshes in</div>
{/if}
```

Use `tick` for DOM manipulation during transition:

```svelte
<!file: App.svelte>
<script>
	export let visible = false;

	/**
	 * @param {HTMLElement} node
	 * @param {{ speed?: number }} params
	 */
	function typewriter(node, { speed = 1 }) {
		const valid = node.childNodes.length === 1 && node.childNodes[0].nodeType === Node.TEXT_NODE;

		if (!valid) {
			throw new Error(`This transition only works on elements with a single text node child`);
		}

		const text = node.textContent;
		const duration = text.length / (speed * 0.01);

		return {
			duration,
			tick: (t) => {
				const i = ~~(text.length * t);
				node.textContent = text.slice(0, i);
			}
		};
	}
</script>

{#if visible}
	<p in:typewriter={{ speed: 1 }}>The quick brown fox jumps over the lazy dog</p>
{/if}
```

Returning a function instead of object allows coordination between transitions (crossfade effects).

## Events

Elements with transitions dispatch:
- `introstart`
- `introend` 
- `outrostart`
- `outroend`

```svelte
{#if visible}
	<p
		transition:fly={{ y: 200, duration: 2000 }}
		onintrostart={() => (status = 'intro started')}
		onoutrostart={() => (status = 'outro started')}
		onintroend={() => (status = 'intro ended')}
		onoutroend={() => (status = 'outro ended')}
	>
		Flies in and out
	</p>
{/if}
```

## docs/svelte/03-template-syntax/15-in-and-out.md

# in: and out:

The `in:` and `out:` directives work like [`transition:`](transition) but are unidirectional. Unlike bidirectional transitions, `in` transitions don't reverse when interrupted - they play alongside `out` transitions.

```svelte
<script>
  import { fade, fly } from 'svelte/transition';
  
  let visible = $state(false);
</script>

<label>
  <input type="checkbox" bind:checked={visible}>
  visible
</label>

{#if visible}
	<div in:fly={{ y: 200 }} out:fade>flies in, fades out</div>
{/if}
```

**Key difference**: If an out transition is aborted, transitions restart from scratch rather than reversing.

## docs/svelte/03-template-syntax/16-animate.md

# animate:

Animations trigger when keyed each block contents are re-ordered. Only runs when existing item index changes, not on add/remove. Must be on immediate child of keyed each block.

```svelte
{#each list as item, index (item)}
	<li animate:flip>{item}</li>
{/each}
```

## Animation Parameters

```svelte
{#each list as item, index (item)}
	<li animate:flip={{ delay: 500 }}>{item}</li>
{/each}
```

## Custom Animation Functions

```js
animation = (node: HTMLElement, { from: DOMRect, to: DOMRect } , params: any) => {
	delay?: number,
	duration?: number,
	easing?: (t: number) => number,
	css?: (t: number, u: number) => string,
	tick?: (t: number, u: number) => void
}
```

Function receives `node`, `animation` object with `from`/`to` DOMRects, and `parameters`. `from` is start position, `to` is end position after reorder.

If returns `css` method, Svelte creates web animation. `t` goes from 0-1 after easing, `u` equals `1-t`.

```svelte
<script>
	import { cubicOut } from 'svelte/easing';

	/**
	 * @param {HTMLElement} node
	 * @param {{ from: DOMRect; to: DOMRect }} states
	 * @param {any} params
	 */
	function whizz(node, { from, to }, params) {
		const dx = from.left - to.left;
		const dy = from.top - to.top;

		const d = Math.sqrt(dx * dx + dy * dy);

		return {
			delay: 0,
			duration: Math.sqrt(d) * 120,
			easing: cubicOut,
			css: (t, u) => `transform: translate(${u * dx}px, ${u * dy}px) rotate(${t * 360}deg);`
		};
	}
</script>

{#each list as item, index (item)}
	<div animate:whizz>{item}</div>
{/each}
```

Can return `tick` function called during animation. Prefer `css` over `tick` - web animations run off main thread.

```svelte
<script>
	import { cubicOut } from 'svelte/easing';

	/**
	 * @param {HTMLElement} node
	 * @param {{ from: DOMRect; to: DOMRect }} states
	 * @param {any} params
	 */
	function whizz(node, { from, to }, params) {
		const dx = from.left - to.left;
		const dy = from.top - to.top;

		const d = Math.sqrt(dx * dx + dy * dy);

		return {
			delay: 0,
			duration: Math.sqrt(d) * 120,
			easing: cubicOut,
			tick: (t, u) => Object.assign(node.style, { color: t > 0.5 ? 'Pink' : 'Blue' })
		};
	}
</script>

{#each list as item, index (item)}
	<div animate:whizz>{item}</div>
{/each}
```

## docs/svelte/03-template-syntax/17-style.md

# style:

The `style:` directive provides shorthand for setting multiple styles on an element.

## Basic Usage

```svelte
<div style:color="red">...</div>
<div style="color: red;">...</div>
```

## Dynamic Values

```svelte
<div style:color={myColor}>...</div>
```

## Shorthand Form

```svelte
<div style:color>...</div>
```

## Multiple Styles

```svelte
<div style:color style:width="12rem" style:background-color={darkMode ? 'black' : 'white'}>...</div>
```

## Important Modifier

```svelte
<div style:color|important="red">...</div>
```

## Precedence

`style:` directives take precedence over `style` attributes, even over `!important`:

```svelte
<div style:color="red" style="color: blue">This will be red</div>
<div style:color="red" style="color: blue !important">This will still be red</div>
```

## docs/svelte/03-template-syntax/18-class.md

# class

Two ways to set classes: `class` attribute and `class:` directive.

## Attributes

### Primitive values
```svelte
<div class={large ? 'large' : 'small'}>...</div>
```

> Falsy values stringify (`class="false"`), except `undefined`/`null` which omit the attribute.

### Objects and arrays

Since Svelte 5.16, `class` accepts objects/arrays, converted using [clsx](https://github.com/lukeed/clsx).

**Objects** - truthy keys are added:
```svelte
<script>
	let { cool } = $props();
</script>

<div class={{ cool, lame: !cool }}>...</div>
```

**Arrays** - truthy values combined:
```svelte
<div class={[faded && 'saturate-0 opacity-50', large && 'scale-200']}>...</div>
```

**Nested arrays/objects** - useful for combining local classes with props:
```svelte
<!file: Button.svelte>
<script>
	let props = $props();
</script>

<button {...props} class={['cool-button', props.class]}>
	{@render props.children?.()}
</button>
```

```svelte
<!file: App.svelte>
<script>
	import Button from './Button.svelte';
	let useTailwind = $state(false);
</script>

<Button
	onclick={() => useTailwind = true}
	class={{ 'bg-blue-700 sm:w-1/2': useTailwind }}
>
	Accept the inevitability of Tailwind
</Button>
```

**TypeScript** - use `ClassValue` type:
```svelte
<script lang="ts">
	import type { ClassValue } from 'svelte/elements';

	const props: { class: ClassValue } = $props();
</script>

<div class={['original', props.class]}>...</div>
```

## The `class:` directive

Legacy conditional class setting (pre-5.16):

```svelte
<div class={{ cool, lame: !cool }}>...</div>
<div class:cool={cool} class:lame={!cool}>...</div>
```

**Shorthand** when class name matches value:
```svelte
<div class:cool class:lame={!cool}>...</div>
```

> Consider using `class` attribute instead - more powerful and composable.

## docs/svelte/03-template-syntax/19-await-expressions.md

# await

Use `await` in three places (experimental in Svelte 5.36):
- Top level of component `<script>`
- Inside `$derived(...)` declarations  
- Inside markup

Enable with:

```js
/// file: svelte.config.js
export default {
	compilerOptions: {
		experimental: {
			async: true
		}
	}
};
```

## Boundaries

Must use `await` inside `<svelte:boundary>` with `pending` snippet:

```svelte
<svelte:boundary>
	<MyApp />

	{#snippet pending()}
		<p>loading...</p>
	{/snippet}
</svelte:boundary>
```

## Synchronized updates

State changes wait for async work to complete to prevent inconsistent UI:

```svelte
<script>
	let a = $state(1);
	let b = $state(2);

	async function add(a, b) {
		await new Promise((f) => setTimeout(f, 500)); // artificial delay
		return a + b;
	}
</script>

<input type="number" bind:value={a}>
<input type="number" bind:value={b}>

<p>{a} + {b} = {await add(a, b)}</p>
```

When `a` increments, UI won't show `2 + 2 = 3` but waits to show `2 + 2 = 4`.

## Concurrency

Independent `await` expressions run in parallel:

```svelte
<p>{await one()}</p>
<p>{await two()}</p>
```

Sequential `await` in `<script>` runs sequentially. Independent `$derived` expressions update independently:

```js
// these will run sequentially the first time,
// but will update independently
let a = $derived(await one());
let b = $derived(await two());
```

## Loading states

Use `$effect.pending()` or `settled()`:

```js
import { tick, settled } from 'svelte';

async function onclick() {
	updating = true;

	// without this, the change to `updating` will be
	// grouped with the other changes, meaning it
	// won't be reflected in the UI
	await tick();

	color = 'octarine';
	answer = 42;

	await settled();

	// any updates affected by `color` or `answer`
	// have now been applied
	updating = false;
}
```

## Error handling

Errors bubble to nearest error boundary.

## Caveats

- Experimental - subject to breaking changes
- SSR is synchronous - only `pending` snippet renders during SSR
- Block effects run before `$effect.pre`/`beforeUpdate` when async enabled

## docs/svelte/04-styling/01-scoped-styles.md

# Scoped styles

Svelte components include `<style>` elements with CSS scoped by default using a hash-based class (e.g. `svelte-123xyz`).

```svelte
<style>
	p {
		/* this will only affect <p> elements in this component */
		color: burlywood;
	}
</style>
```

## Specificity

Scoped selectors get +0-1-0 specificity from the scoping class, so component styles override global styles even if global stylesheet loads later.

Multiple scoping classes use `:where(.svelte-xyz123)` to avoid further specificity increases.

## Scoped keyframes

`@keyframes` names are scoped to components. Animation rules adjust automatically:

```svelte
<style>
	.bouncy {
		animation: bounce 10s;
	}

	/* these keyframes are only accessible inside this component */
	@keyframes bounce {
		/* ... */
	}
</style>
```

## docs/svelte/04-styling/02-global-styles.md

# Global styles

## :global(...)

Apply styles to a single selector globally:

```svelte
<style>
	:global(body) {
		/* applies to <body> */
		margin: 0;
	}

	div :global(strong) {
		/* applies to all <strong> elements, in any component,
		   that are inside <div> elements belonging
		   to this component */
		color: goldenrod;
	}

	p:global(.big.red) {
		/* applies to all <p> elements belonging to this component
		   with `class="big red"`, even if it is applied
		   programmatically (for example by a library) */
	}
</style>
```

For global keyframes, prepend with `-global-`:

```svelte
<style>
	@keyframes -global-my-animation-name {
		/* code goes here */
	}
</style>
```

## :global

Apply styles to multiple selectors globally using blocks:

```svelte
<style>
	:global {
		/* applies to every <div> in your application */
		div { ... }

		/* applies to every <p> in your application */
		p { ... }
	}

	.a :global {
		/* applies to every `.b .c .d` element, in any component,
		   that is inside an `.a` element in this component */
		.b .c .d {...}
	}
</style>
```

## docs/svelte/04-styling/03-custom-properties.md

# Custom Properties

Pass CSS custom properties (static and dynamic) to components:

```svelte
<Slider
	bind:value
	min={0}
	max={100}
	--track-color="black"
	--thumb-color="rgb({r} {g} {b})"
/>
```

This desugars to a wrapper element:

```svelte
<svelte-css-wrapper style="display: contents; --track-color: black; --thumb-color: rgb({r} {g} {b})">
	<Slider
		bind:value
		min={0}
		max={100}
	/>
</svelte-css-wrapper>
```

For SVG elements, uses `<g>`:

```svelte
<g style="--track-color: black; --thumb-color: rgb({r} {g} {b})">
	<Slider
		bind:value
		min={0}
		max={100}
	/>
</g>
```

Inside components, use `var()` with fallbacks:

```svelte
<style>
	.track {
		background: var(--track-color, #aaa);
	}

	.thumb {
		background: var(--thumb-color, blue);
	}
</style>
```

Custom properties inherit from parent elements. Define on `:root` for global access.

> **Note:** Wrapper element won't affect layout but may affect CSS selectors using `>` combinator.

## docs/svelte/04-styling/04-nested-style-elements.md

# Nested `<style>` elements

Only one top-level `<style>` tag allowed per component.

`<style>` tags nested inside elements/logic blocks are inserted as-is into DOM with no scoping or processing.

```svelte
<div>
	<style>
		/* this style tag will be inserted as-is */
		div {
			/* this will apply to all `<div>` elements in the DOM */
			color: red;
		}
	</style>
</div>
```

## docs/svelte/05-special-elements/01-svelte-boundary.md

# `<svelte:boundary>`

```svelte
<svelte:boundary onerror={handler}>...</svelte:boundary>
```

Error boundaries catch rendering errors and provide fallback UI. They can also handle loading states for `await` expressions.

**Key limitations:**
- Only catch errors during rendering and in `$effect` 
- Do NOT catch errors in event handlers or async callbacks

## Properties

### `pending`

Shows loading UI for `await` expressions inside the boundary:

```svelte
<svelte:boundary>
	<p>{await delayed('hello!')}</p>

	{#snippet pending()}
		<p>loading...</p>
	{/snippet}
</svelte:boundary>
```

Only shows on initial load, not subsequent updates (use `$effect.pending()` for those).

### `failed`

Renders when errors occur, provides `error` and `reset` function:

```svelte
<svelte:boundary>
	<FlakyComponent />

	{#snippet failed(error, reset)}
		<button onclick={reset}>oops! try again</button>
	{/snippet}
</svelte:boundary>
```

### `onerror`

Handler function called with same `error` and `reset` arguments:

```svelte
<script>
	let error = $state(null);
	let reset = $state(() => {});

	function onerror(e, r) {
		error = e;
		reset = r;
	}
</script>

<svelte:boundary {onerror}>
	<FlakyComponent />
</svelte:boundary>

{#if error}
	<button onclick={() => {
		error = null;
		reset();
	}}>
		oops! try again
	</button>
{/if}
```

Useful for error reporting or handling errors outside the boundary. Errors in `onerror` bubble to parent boundaries.

## docs/svelte/05-special-elements/02-svelte-window.md

# `<svelte:window>`

```svelte
<svelte:window onevent={handler} />
<svelte:window bind:prop={value} />
```

Adds event listeners to `window` object. Auto-removes on component destroy. SSR-safe.

Must be at component top level, not inside blocks/elements.

## Event Listeners

```svelte
<script>
	function handleKeydown(event) {
		alert(`pressed the ${event.key} key`);
	}
</script>

<svelte:window onkeydown={handleKeydown} />
```

## Bindable Properties

```svelte
<svelte:window bind:scrollY={y} />
```

**Readonly:** `innerWidth`, `innerHeight`, `outerWidth`, `outerHeight`, `online`, `devicePixelRatio`

**Writable:** `scrollX`, `scrollY`

> **Note:** Initial scroll binding won't scroll page (accessibility). Use `scrollTo()` in `$effect` if needed.

## docs/svelte/05-special-elements/03-svelte-document.md

# `<svelte:document>`

Adds event listeners and actions to the `document` object. Must be at component top level, never inside blocks or elements.

## Syntax

```svelte
<svelte:document onevent={handler} />
<svelte:document bind:prop={value} />
```

## Usage

```svelte
<svelte:document onvisibilitychange={handleVisibilityChange} use:someAction />
```

## Bindable Properties (readonly)

- `activeElement`
- `fullscreenElement` 
- `pointerLockElement`
- `visibilityState`

## docs/svelte/05-special-elements/04-svelte-body.md

# `<svelte:body>`

```svelte
<svelte:body onevent={handler} />
```

Adds event listeners to `document.body` for events that don't fire on `window` (like `mouseenter`/`mouseleave`) and allows using actions on `<body>`.

**Requirements:**
- Must be at top level of component
- Cannot be inside blocks or elements

```svelte
<svelte:body onmouseenter={handleMouseenter} onmouseleave={handleMouseleave} use:someAction />
```

## docs/svelte/05-special-elements/05-svelte-head.md

# `<svelte:head>`

```svelte
<svelte:head>...</svelte:head>
```

Inserts elements into `document.head`. During SSR, head content is exposed separately from body content.

Must be at component top level, never inside blocks or elements.

```svelte
<svelte:head>
	<title>Hello world!</title>
	<meta name="description" content="This is where the description goes for SEO" />
</svelte:head>
```

## docs/svelte/05-special-elements/06-svelte-element.md

# `<svelte:element>`

```svelte
<svelte:element this={expression} />
```

Renders dynamic elements unknown at author time (e.g., from CMS).

## Usage

```svelte
<script>
	let tag = $state('hr');
</script>

<svelte:element this={tag}>
	This text cannot appear inside an hr element
</svelte:element>
```

## Key Points

- Properties and event listeners are applied to the element
- Only `bind:this` binding supported
- If `this` is nullish, element and children won't render
- Void elements (e.g., `br`) with children throw runtime error in dev
- Use `xmlns` attribute for explicit namespace:

```svelte
<svelte:element this={tag} xmlns="http://www.w3.org/2000/svg" />
```

- `this` must be valid DOM element tag (not `#text` or `svelte:head`)

## docs/svelte/05-special-elements/07-svelte-options.md

# `<svelte:options>`

```svelte
<svelte:options option={value} />
```

Specifies per-component compiler options.

## Options

- `runes={true}` — forces runes mode
- `runes={false}` — forces legacy mode  
- `namespace="..."` — "html" (default), "svg", or "mathml"
- `customElement={...}` — custom element options, or string for tag name
- `css="injected"` — injects styles inline (`<style>` tag in SSR, JS in client)

```svelte
<svelte:options customElement="my-custom-element" />
```

> **Deprecated in Svelte 5:** `immutable`, `accessors` (non-functional in runes mode)

## docs/svelte/06-runtime/01-stores.md

# Stores

A store is an object that allows reactive access to a value via a simple store contract. Access store values in components by prefixing with `$`.

```svelte
<script>
	import { writable } from 'svelte/store';

	const count = writable(0);
	console.log($count); // logs 0

	count.set(1);
	console.log($count); // logs 1

	$count = 2;
	console.log($count); // logs 2
</script>
```

**Rules:**
- Store must be declared at component top level
- Local variables must NOT have `$` prefix
- `$`-prefixed assignments require writable store

## When to use stores

With Svelte 5 runes, stores are less needed:

**Use runes instead for:**
- Extracting logic: Use runes in `.svelte.js`/`.svelte.ts` files
- Shared state: Create `$state` objects

```ts
/// file: state.svelte.js
export const userState = $state({
	name: 'name',
	/* ... */
});
```

```svelte
<!file: App.svelte>
<script>
	import { userState } from './state.svelte.js';
</script>

<p>User name: {userState.name}</p>
<button onclick={() => {
	userState.name = 'new name';
}}>
	change name
</button>
```

**Still use stores for:**
- Complex asynchronous data streams
- Manual control over updates/listening
- RxJs compatibility

## svelte/store

### `writable`

Creates store with `set` and `update` methods.

```js
/// file: store.js
import { writable } from 'svelte/store';

const count = writable(0);

count.subscribe((value) => {
	console.log(value);
}); // logs '0'

count.set(1); // logs '1'

count.update((n) => n + 1); // logs '2'
```

Optional second argument - function called when subscribers go from 0→1, must return stop function:

```js
/// file: store.js
import { writable } from 'svelte/store';

const count = writable(0, () => {
	console.log('got a subscriber');
	return () => console.log('no more subscribers');
});

count.set(1); // does nothing

const unsubscribe = count.subscribe((value) => {
	console.log(value);
}); // logs 'got a subscriber', then '1'

unsubscribe(); // logs 'no more subscribers'
```

### `readable`

Creates read-only store. Same API as `writable` but no external `set`/`update`.

```ts
import { readable } from 'svelte/store';

const time = readable(new Date(), (set) => {
	set(new Date());

	const interval = setInterval(() => {
		set(new Date());
	}, 1000);

	return () => clearInterval(interval);
});

const ticktock = readable('tick', (set, update) => {
	const interval = setInterval(() => {
		update((sound) => (sound === 'tick' ? 'tock' : 'tick'));
	}, 1000);

	return () => clearInterval(interval);
});
```

### `derived`

Derives store from other stores. Runs when first subscriber subscribes and when dependencies change.

Simple version:
```ts
import { derived } from 'svelte/store';

const doubled = derived(a, ($a) => $a * 2);
```

Async with `set`/`update`:
```ts
import { derived } from 'svelte/store';

const delayed = derived(
	a,
	($a, set) => {
		setTimeout(() => set($a), 1000);
	},
	2000
);

const delayedIncrement = derived(a, ($a, set, update) => {
	set($a);
	setTimeout(() => update((x) => x + 1), 1000);
	// every time $a produces a value, this produces two
	// values, $a immediately and then $a + 1 a second later
});
```

Return cleanup function:
```ts
import { derived } from 'svelte/store';

const tick = derived(
	frequency,
	($frequency, set) => {
		const interval = setInterval(() => {
			set(Date.now());
		}, 1000 / $frequency);

		return () => {
			clearInterval(interval);
		};
	},
	2000
);
```

Multiple stores:
```ts
import { derived } from 'svelte/store';

const summed = derived([a, b], ([$a, $b]) => $a + $b);

const delayed = derived([a, b], ([$a, $b], set) => {
	setTimeout(() => set($a + $b), 1000);
});
```

### `readonly`

Makes store read-only:

```js
import { readonly, writable } from 'svelte/store';

const writableStore = writable(1);
const readableStore = readonly(writableStore);

readableStore.subscribe(console.log);

writableStore.set(2); // console: 2
// @errors: 2339
readableStore.set(2); // ERROR
```

### `get`

Retrieves store value without subscribing. Not recommended in hot code paths.

```ts
import { get } from 'svelte/store';

const value = get(store);
```

## Store contract

```ts
store = { subscribe: (subscription: (value: any) => void) => (() => void), set?: (value: any) => void }
```

Custom stores must implement:

1. `.subscribe(fn)` - calls `fn` immediately with current value, returns unsubscribe function
2. `.set(value)` (optional) - updates value, calls all subscribers synchronously

Compatible with RxJS Observables.

## docs/svelte/06-runtime/02-context.md

# Context

Context allows components to access values from parent components without prop-drilling.

## Basic Usage

Parent sets context with `setContext(key, value)`:

```svelte
<!file: Parent.svelte>
<script>
	import { setContext } from 'svelte';

	setContext('my-context', 'hello from Parent.svelte');
</script>
```

Child retrieves with `getContext`:

```svelte
<!file: Child.svelte>
<script>
	import { getContext } from 'svelte';

	const message = getContext('my-context');
</script>

<h1>{message}, inside Child.svelte</h1>
```

Works with children snippets:

```svelte
<Parent>
	<Child />
</Parent>
```

Key and value can be any JavaScript value.

Available functions: `setContext`, `getContext`, `hasContext`, `getAllContexts`.

## Using Context with State

Store reactive state in context:

```svelte
<script>
	import { setContext } from 'svelte';
	import Child from './Child.svelte';

	let counter = $state({
		count: 0
	});

	setContext('counter', counter);
</script>

<button onclick={() => counter.count += 1}>
	increment
</button>

<Child />
<Child />
<Child />
```

**Important**: Update properties, don't reassign objects:

```svelte
<!-- ❌ Breaks the link -->
<button onclick={() => counter = { count: 0 }}>
	reset
</button>

<!-- ✅ Correct -->
<button onclick={() =>counter.count = 0}>
	reset
</button>
```

## Type-safe Context

Wrap context calls in helper functions:

```js
/// file: context.js
// @filename: ambient.d.ts
interface User {}

// @filename: index.js
//cut
import { getContext, setContext } from 'svelte';

const key = {};

/** @param {User} user */
export function setUserContext(user) {
	setContext(key, user);
}

export function getUserContext() {
	return /** @type {User} */ (getContext(key));
}
```

## Replacing Global State

Avoid global state modules during SSR:

```js
/// file: state.svelte.js
export const myGlobalState = $state({
	user: {
		// ...
	}
	// ...
});
```

This can leak data between SSR requests:

```svelte
<!file: App.svelte->
<script>
	import { myGlobalState } from './state.svelte.js';

	let { data } = $props();

	if (data.user) {
		myGlobalState.user = data.user;
	}
</script>
```

Context solves this by being request-scoped.

## docs/svelte/06-runtime/03-lifecycle-hooks.md

# Lifecycle hooks

Svelte 5 lifecycle has only creation and destruction. No "before/after update" hooks - effects handle granular updates instead.

## `onMount`

Runs after component mounts to DOM. Must be called during component initialization. Doesn't run on server.

```svelte
<script>
	import { onMount } from 'svelte';

	onMount(() => {
		console.log('the component has mounted');
	});
</script>
```

Return function for cleanup on unmount:

```svelte
<script>
	import { onMount } from 'svelte';

	onMount(() => {
		const interval = setInterval(() => {
			console.log('beep');
		}, 1000);

		return () => clearInterval(interval);
	});
</script>
```

> **Note:** Only works with synchronous return. `async` functions return Promise, can't synchronously return cleanup function.

## `onDestroy`

Runs before component unmounts. Only lifecycle hook that runs server-side.

```svelte
<script>
	import { onDestroy } from 'svelte';

	onDestroy(() => {
		console.log('the component is being destroyed');
	});
</script>
```

## `tick`

Promise that resolves after pending state changes apply. Use instead of "after update" hook.

```svelte
<script>
	import { tick } from 'svelte';

	$effect.pre(() => {
		console.log('the component is about to update');
		tick().then(() => {
				console.log('the component just updated');
		});
	});
</script>
```

## Migration from Svelte 4

- Replace `beforeUpdate` with `$effect.pre`
- Replace `afterUpdate` with `$effect`
- Effects only react to referenced state, more granular than component-wide hooks

### Chat autoscroll example

```svelte
<script>
	import {beforeUpdate, afterUpdate,tick } from 'svelte';

	let updatingMessages = false;
	let theme =$state('dark');
	let messages =$state([]);

	let viewport;

	beforeUpdate(() => {
	$effect.pre(() => {
		if (!updatingMessages) return;
		messages;
		const autoscroll = viewport && viewport.offsetHeight + viewport.scrollTop > viewport.scrollHeight - 50;

		if (autoscroll) {
			tick().then(() => {
				viewport.scrollTo(0, viewport.scrollHeight);
			});
		}

		updatingMessages = false;
	});

	function handleKeydown(event) {
		if (event.key === 'Enter') {
			const text = event.target.value;
			if (!text) return;

			updatingMessages = true;
			messages = [...messages, text];
			event.target.value = '';
		}
	}

	function toggle() {
		theme = theme === 'dark' ? 'light' : 'dark';
	}
</script>

<div class:dark={theme === 'dark'}>
	<div bind:this={viewport}>
		{#each messages as message}
			<p>{message}</p>
		{/each}
	</div>

	<inputonkeydown={handleKeydown} />

	<buttononclick={toggle}> Toggle dark mode </button>
</div>
```

## docs/svelte/06-runtime/04-imperative-component-api.md

# Imperative component API

Functions for creating and managing Svelte components imperatively.

## `mount`

Instantiates and mounts a component to a target element:

```js
// @errors: 2322
import { mount } from 'svelte';
import App from './App.svelte';

const app = mount(App, {
	target: document.querySelector('#app'),
	props: { some: 'property' }
});
```

**Note:** Effects (including `onMount` callbacks and actions) don't run during `mount`. Use `flushSync()` to force pending effects to run.

## `unmount`

Unmounts a component created with `mount` or `hydrate`:

```js
import { mount, unmount } from 'svelte';
import App from './App.svelte';

const app = mount(App, { target: document.body });

// later
unmount(app, { outro: true });
```

Returns a `Promise` that resolves after transitions complete if `options.outro` is true.

## `render`

Server-only function that renders a component to HTML:

```js
// @errors: 2724 2305 2307
import { render } from 'svelte/server';
import App from './App.svelte';

const result = render(App, {
	props: { some: 'property' }
});
result.body; // HTML for somewhere in this <body> tag
result.head; // HTML for somewhere in this <head> tag
```

## `hydrate`

Like `mount`, but reuses existing SSR HTML and makes it interactive:

```js
// @errors: 2322
import { hydrate } from 'svelte';
import App from './App.svelte';

const app = hydrate(App, {
	target: document.querySelector('#app'),
	props: { some: 'property' }
});
```

**Note:** Effects don't run during `hydrate`. Use `flushSync()` immediately after if needed.

## docs/svelte/07-misc/02-testing.md

# Testing

Testing frameworks help you write assertions about code behavior. Svelte works with [Vitest](https://vitest.dev/), [Jasmine](https://jasmine.github.io/), [Cypress](https://www.cypress.io/) and [Playwright](https://playwright.dev/).

## Unit and integration testing using Vitest

For Vite/SvelteKit projects, use [Vitest](https://vitest.dev/). Setup via Svelte CLI or manually:

```sh
npm install -D vitest
```

```js
/// file: vite.config.js
import { defineConfig } from'vitest/config';

export default defineConfig({
	// ...
	// Tell Vitest to use the `browser` entry points in `package.json` files, even though it's running in Node
	resolve: process.env.VITEST
		? {
				conditions: ['browser']
			}
		: undefined
});
```

### Testing runes

Test `.js/.ts` files with runes:

```js
/// file: multiplier.svelte.test.js
import { flushSync } from 'svelte';
import { expect, test } from 'vitest';
import { multiplier } from './multiplier.svelte.js';

test('Multiplier', () => {
	let double = multiplier(0, 2);

	expect(double.value).toEqual(0);

	double.set(5);

	expect(double.value).toEqual(10);
});
```

```js
/// file: multiplier.svelte.js
/**
 * @param {number} initial
 * @param {number} k
 */
export function multiplier(initial, k) {
	let count = $state(initial);

	return {
		get value() {
			return count * k;
		},
		/** @param {number} c */
		set: (c) => {
			count = c;
		}
	};
}
```

Use runes in test files (filename must include `.svelte`):

```js
/// file: multiplier.svelte.test.js
import { flushSync } from 'svelte';
import { expect, test } from 'vitest';
import { multiplier } from './multiplier.svelte.js';

test('Multiplier', () => {
	let count = $state(0);
	let double = multiplier(() => count, 2);

	expect(double.value).toEqual(0);

	count = 5;

	expect(double.value).toEqual(10);
});
```

For effects, wrap tests in `$effect.root`:

```js
/// file: logger.svelte.test.js
import { flushSync } from 'svelte';
import { expect, test } from 'vitest';
import { logger } from './logger.svelte.js';

test('Effect', () => {
	const cleanup = $effect.root(() => {
		let count = $state(0);

		// logger uses an $effect to log updates of its input
		let log = logger(() => count);

		// effects normally run after a microtask,
		// use flushSync to execute all pending effects synchronously
		flushSync();
		expect(log).toEqual([0]);

		count = 1;
		flushSync();

		expect(log).toEqual([0, 1]);
	});

	cleanup();
});
```

### Component testing

Install jsdom for DOM environment:

```sh
npm install -D jsdom
```

```js
/// file: vite.config.js
import { defineConfig } from 'vitest/config';

export default defineConfig({
	plugins: [
		/* ... */
	],
	test: {
		// If you are testing components client-side, you need to setup a DOM environment.
		// If not all your files should have this environment, you can use a
		// `// @vitest-environment jsdom` comment at the top of the test files instead.
		environment: 'jsdom'
	},
	// Tell Vitest to use the `browser` entry points in `package.json` files, even though it's running in Node
	resolve: process.env.VITEST
		? {
				conditions: ['browser']
			}
		: undefined
});
```

Basic component test:

```js
/// file: component.test.js
import { flushSync, mount, unmount } from 'svelte';
import { expect, test } from 'vitest';
import Component from './Component.svelte';

test('Component', () => {
	// Instantiate the component using Svelte's `mount` API
	const component = mount(Component, {
		target: document.body, // `document` exists because of jsdom
		props: { initial: 0 }
	});

	expect(document.body.innerHTML).toBe('<button>0</button>');

	// Click the button, then flush the changes so you can synchronously write expectations
	document.body.querySelector('button').click();
	flushSync();

	expect(document.body.innerHTML).toBe('<button>1</button>');

	// Remove the component from the DOM
	unmount(component);
});
```

Using [@testing-library/svelte](https://testing-library.com/docs/svelte-testing-library/intro/):

```js
/// file: component.test.js
import { render, screen } from '@testing-library/svelte';
import userEvent from '@testing-library/user-event';
import { expect, test } from 'vitest';
import Component from './Component.svelte';

test('Component', async () => {
	const user = userEvent.setup();
	render(Component);

	const button = screen.getByRole('button');
	expect(button).toHaveTextContent(0);

	await user.click(button);
	expect(button).toHaveTextContent(1);
});
```

## E2E tests using Playwright

Setup with Svelte CLI or `npm init playwright`. Configure for non-Vite projects:

```js
/// file: playwright.config.js
const config = {
	webServer: {
		command: 'npm run build && npm run preview',
		port: 4173
	},
	testDir: 'tests',
	testMatch: /(.+\.)?(test|spec)\.[jt]s/
};

export default config;
```

Write DOM-focused tests:

```js
// @errors: 2307 7031
/// file: tests/hello-world.spec.js
import { expect, test } from '@playwright/test';

test('home page has expected h1', async ({ page }) => {
	await page.goto('/');
	await expect(page.locator('h1')).toBeVisible();
});
```

## docs/svelte/07-misc/03-typescript.md

# TypeScript

Use TypeScript in Svelte components with IDE support via [Svelte VS Code extension](https://marketplace.visualstudio.com/items?itemName=svelte.svelte-vscode) and [`svelte-check`](https://www.npmjs.com/package/svelte-check).

## `<script lang="ts">`

Add `lang="ts"` to script tags:

```svelte
<script lang="ts">
	let name: string = 'world';

	function greet(name: string) {
		alert(`Hello, ${name}!`);
	}
</script>

<button onclick={(e: Event) => greet(e.target.innerText)}>
	{name as string}
</button>
```

**Limitations**: Only type-only features supported (annotations, interfaces). Not supported: enums, constructor modifiers, non-standard ECMAScript features.

## Preprocessor Setup

For full TypeScript features, add preprocessor:

```ts
/// file: svelte.config.js
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

const config = {
	preprocess: vitePreprocess({ script: true })
};

export default config;
```

**SvelteKit/Vite**: Use `npx sv create` or `npm create vite@latest` (svelte-ts option).

## tsconfig.json Settings

Required settings:
- `target`: at least `ES2015`
- `verbatimModuleSyntax`: `true`
- `isolatedModules`: `true`

## Typing `$props`

```svelte
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		requiredProperty: number;
		optionalProperty?: boolean;
		snippetWithStringArgument: Snippet<[string]>;
		eventHandler: (arg: string) => void;
		[key: string]: unknown;
	}

	let {
		requiredProperty,
		optionalProperty,
		snippetWithStringArgument,
		eventHandler,
		...everythingElse
	}: Props = $props();
</script>

<button onclick={() => eventHandler('clicked button')}>
	{@render snippetWithStringArgument('hello')}
</button>
```

## Generic `$props`

```svelte
<script lang="ts" generics="Item extends { text: string }">
	interface Props {
		items: Item[];
		select(item: Item): void;
	}

	let { items, select }: Props = $props();
</script>

{#each items as item}
	<button onclick={() => select(item)}>
		{item.text}
	</button>
{/each}
```

## Typing Wrapper Components

Use `svelte/elements` interfaces:

```svelte
<script lang="ts">
	import type { HTMLButtonAttributes } from 'svelte/elements';

	let { children, ...rest }: HTMLButtonAttributes = $props();
</script>

<button {...rest}>
	{@render children?.()}
</button>
```

For elements without dedicated types:

```svelte
<script lang="ts">
	import type { SvelteHTMLElements } from 'svelte/elements';

	let { children, ...rest }: SvelteHTMLElements['div'] = $props();
</script>

<div {...rest}>
	{@render children?.()}
</div>
```

## Typing `$state`

```ts
let count: number = $state(0);
```

Without initial value, type includes `undefined`. Use `as` casting when needed:

```ts
class Counter {
	count = $state() as number;
	constructor(initial: number) {
		this.count = initial;
	}
}
```

## The `Component` Type

Restrict dynamic components:

```svelte
<script lang="ts">
	import type { Component } from 'svelte';

	interface Props {
		DynamicComponent: Component<{ prop: string }>;
	}

	let { DynamicComponent }: Props = $props();
</script>

<DynamicComponent prop="foo" />
```

Extract component props:

```ts
import type { Component, ComponentProps } from 'svelte';
import MyComponent from './MyComponent.svelte';

function withProps<TComponent extends Component<any>>(
	component: TComponent,
	props: ComponentProps<TComponent>
) {}

withProps(MyComponent, { foo: 'bar' });
```

Component constructor/instance types:

```svelte
<script lang="ts">
	import MyComponent from './MyComponent.svelte';

	let componentConstructor: typeof MyComponent = MyComponent;
	let componentInstance: MyComponent;
</script>

<MyComponent bind:this={componentInstance} />
```

## Enhancing DOM Types

Augment `svelte/elements` for custom attributes/events:

```ts
/// file: additional-svelte-typings.d.ts
import { HTMLButtonAttributes } from 'svelte/elements';

declare module 'svelte/elements' {
	export interface SvelteHTMLElements {
		'custom-button': HTMLButtonAttributes;
	}

	export interface HTMLAttributes<T> {
		globalattribute?: string;
	}

	export interface HTMLButtonAttributes {
		veryexperimentalattribute?: string;
	}
}

export {};
```

Ensure the `.d.ts` file is included in `tsconfig.json`.

Instructions for LLMs: <s>Always use Svelte 5 runes and Svelte 5 syntax. Runes do not need to be imported, they are globals. $state() runes are always declared using `let`, never with `const`. When passing a function to $derived, you must always use $derived.by(() => ...). Error boundaries can only catch errors during component rendering and at the top level of an $effect inside the error boundary. Error boundaries do not catch errors in onclick or other event handlers.</s>