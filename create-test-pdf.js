// Simple script to create a test PDF for testing the invoice processor
import fs from 'fs';
import { createCanvas } from 'canvas';

// Create a simple test invoice PDF content
const testInvoiceText = `
INVOICE

Supplier: Test Company B.V.
Address: 123 Test Street, Amsterdam, Netherlands
Phone: +31 20 123 4567
Email: <EMAIL>

Invoice Number: INV-2024-001
Invoice Date: 2024-01-15
Due Date: 2024-02-15

Bill To:
Customer Company
456 Customer Lane
Rotterdam, Netherlands

Description                    Qty    Price    Total
Web Development Services        1     €2,500   €2,500.00
Hosting Services               12     €50      €600.00
                                              --------
Subtotal:                                    €3,100.00
VAT (21%):                                   €651.00
                                              --------
Total Amount:                                €3,751.00

Payment Terms: Net 30 days
Bank: NL91 ABNA 0417 1643 00

Thank you for your business!
`;

console.log('Test invoice content:');
console.log(testInvoiceText);
console.log('\nTo test the application:');
console.log('1. Create a PDF with this content using any PDF creator');
console.log('2. Upload it to the application at http://localhost:5173');
console.log('3. Click "Start Processing" to test the AI extraction');
