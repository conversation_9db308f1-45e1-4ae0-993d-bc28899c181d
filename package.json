{"name": "test-20250810-auto-process-supplier-invoices", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.2", "@sveltejs/kit": "^2.27.3", "@sveltejs/vite-plugin-svelte": "^6.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@triplon/eslint-config": "file:/Users/<USER>/Work/Projects/ewildee/test-20240420-svelte-5/packages/eslint-config", "@types/bun": "^1.2.19", "@vitest/browser": "^3.2.4", "eslint": "^9.33.0", "globals": "^16.3.0", "playwright": "^1.54.2", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "^5.38.0", "svelte-check": "^4.3.1", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vite": "^7.1.1", "vite-plugin-devtools-json": "^0.4.1", "vitest": "^3.2.4", "vitest-browser-svelte": "1.1.0"}, "dependencies": {"canvas": "^3.1.2", "openai": "^5.12.2", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "sharp": "^0.34.3", "tesseract.js": "^6.0.1", "zod": "^4.0.17"}}