// Simple test script to test the upload API endpoint
import fs from 'fs';
import path from 'path';

const testInvoiceContent = `
INVOICE

Supplier: Test Company B.V.
Address: 123 Test Street, Amsterdam, Netherlands
Phone: +31 20 123 4567
Email: <EMAIL>

Invoice Number: INV-2024-001
Invoice Date: 2024-01-15
Due Date: 2024-02-15

Bill To:
Customer Company
456 Customer Lane
Rotterdam, Netherlands

Description                    Qty    Price    Total
Web Development Services        1     €2,500   €2,500.00
Hosting Services               12     €50      €600.00
                                              --------
Subtotal:                                    €3,100.00
VAT (21%):                                   €651.00
                                              --------
Total Amount:                                €3,751.00

Payment Terms: Net 30 days
Bank: NL91 ABNA 0417 1643 00

Thank you for your business!
`;

// Create a simple text file that we can test with (since we can't easily create a PDF programmatically)
const testFilePath = path.join(process.cwd(), 'test-invoice.txt');
fs.writeFileSync(testFilePath, testInvoiceContent);

console.log('Created test file:', testFilePath);
console.log('Content:', testInvoiceContent);

// Test the upload API
async function testUpload() {
  try {
    // Note: This will fail because the API expects a PDF, but we can test the endpoint
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(testFilePath);
    const blob = new Blob([fileBuffer], { type: 'application/pdf' });
    formData.append('file', blob, 'test-invoice.pdf');

    const response = await fetch('http://localhost:5173/api/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    console.log('Upload result:', result);
  } catch (error) {
    console.error('Upload test failed:', error);
  }
}

// Only run the upload test if we're in a browser environment
if (typeof window !== 'undefined') {
  testUpload();
} else {
  console.log('Upload test skipped - run this in a browser environment');
  console.log('To test manually:');
  console.log('1. Open http://localhost:5173 in your browser');
  console.log('2. Create a PDF with the above content');
  console.log('3. Upload it using the drag-and-drop interface');
  console.log('4. Click "Start Processing" to test the complete workflow');
}
