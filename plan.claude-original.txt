Invoice PDF Processing App - Final Implementation Plan

     Overview

     Build a comprehensive SvelteKit invoice processing app with drag-drop upload, queue management, PDF thumbnails, and detailed results display using Gemini 2.5 Flash via OpenRouter.

     Enhanced Features

     - Multi-File Queue System: Left sidebar showing all uploaded files with status tracking
     - PDF Thumbnails: First page preview for each file in the queue
     - Status Management: Visual indicators (queued, processing, complete, failed)
     - Interactive Results: Click any file to view results + rescaled PDF preview
     - Smart Processing: Sequential processing with retry logic
     - Comprehensive Logging: Failed attempts logged and visible in UI

     Core Data Extraction

     - Supplier name, Invoice ID, Invoice date, Due date, Total amount, VAT total
     - Multi-language support (Dutch/English)
     - Confidence indicators and processing metadata

     Technical Architecture

     Frontend Layout (SvelteKit + Svelte 5)

     ┌─────────────────────────────────────────────────────────────┐
     │ Header: Invoice PDF Processor                                │
     ├─────────────┬───────────────────────────────────────────────┤
     │ Queue       │ Main Content Area                             │
     │ Sidebar     │                                               │
     │             │ ┌─ Drag-Drop Zone (when no file selected)     │
     │ 📄 File1 ✅  │ └─ OR                                         │
     │ 📄 File2 🔄  │ ┌─ Selected File Results Panel               │
     │ 📄 File3 ❌  │ │  • Primary invoice data                    │
     │             │ │  • Rescaled PDF preview                     │
     │             │ │  • Metadata & confidence scores             │
     │             │ │  • Error details (if failed)               │
     │             │ └─ Process Another File button                │
     └─────────────┴───────────────────────────────────────────────┘

     Status System

     - 🕒 Queued: Waiting for processing (gray)
     - 🔄 Processing: Currently being processed (blue, animated)
     - ✅ Complete: Successfully processed (green)
     - ❌ Failed: Processing failed after retry (red)
     - ⚠️ Partial: Some data extracted with low confidence (yellow)

     File Processing Pipeline

     1. Upload & Validation: 10MB limit, PDF format, integrity check
     2. Thumbnail Generation: PDF first page → image thumbnail
     3. Queue Management: Add to processing queue with status tracking
     4. Text Extraction: PDF.js → Tesseract.js fallback → status updates
     5. AI Processing: Gemini 2.5 Flash via OpenRouter with retry logic
     6. Results Storage: In-memory results with full metadata
     7. Status Update: Final status with detailed results/errors

     File Structure

     src/
     ├── routes/
     │   ├── +page.svelte (main app layout)
     │   └── api/
     │       ├── process-invoice/+server.ts
     │       ├── generate-thumbnail/+server.ts
     │       └── logs/+server.ts
     ├── lib/
     │   ├── components/
     │   │   ├── FileQueue.svelte (left sidebar with thumbnails)
     │   │   ├── FileDropzone.svelte (upload area)
     │   │   ├── InvoiceResults.svelte (main results panel)
     │   │   ├── PDFPreview.svelte (rescaled PDF display)
     │   │   ├── StatusIcon.svelte (status indicators)
     │   │   ├── ProcessingStatus.svelte (progress tracking)
     │   │   └── MetadataPanel.svelte (confidence & processing info)
     │   ├── types/
     │   │   ├── invoice.ts (data structures)
     │   │   ├── queue.ts (file queue management)
     │   │   └── processing.ts (status, errors, metadata)
     │   ├── utils/
     │   │   ├── pdf-processor.ts (PDF.js + Tesseract.js)
     │   │   ├── thumbnail-generator.ts (PDF → image conversion)
     │   │   ├── gemini-client.ts (OpenRouter integration)
     │   │   ├── queue-manager.ts (file processing queue)
     │   │   ├── validators.ts (Zod schemas)
     │   │   └── logger.ts (comprehensive logging)
     │   └── stores/
     │       ├── fileQueue.ts (queue state management)
     │       ├── selectedFile.ts (current file display)
     │       └── processing.ts (processing status)
     ├── tests/ (comprehensive test suite)
     ├── logs/ (processing logs)
     └── .env.example

     Dependencies (Latest Versions)

     Will verify latest versions using Context7 during implementation:
     {
       "pdf-parse": "latest",
       "pdf2pic": "latest", 
       "tesseract.js": "latest",
       "zod": "latest",
       "@ai-sdk/openai": "latest",
       "ai": "latest",
       "winston": "latest",
       "canvas": "latest" // for thumbnail generation
     }

     Queue Management Features

     - Drag Multiple Files: Add multiple PDFs to queue simultaneously
     - Processing Order: Sequential processing (one at a time)
     - Status Persistence: Maintain status during session
     - Thumbnail Caching: Generate and cache PDF thumbnails
     - Interactive Selection: Click any file to view its results/status
     - Clear Queue: Remove completed/failed files option
     - Retry Individual: Retry failed files from queue

     Results Display Design

     Left Panel (Queue):
     - Thumbnail (64x64px PDF preview)
     - Filename (truncated if long)
     - Status icon with tooltip
     - Processing timestamp

     Main Panel (Selected File):
     - Large PDF preview (rescaled but readable)
     - Primary invoice data in clean card layout
     - Expandable metadata section
     - Error details with retry button (if failed)
     - Copy extracted data functionality

     Implementation Strategy

     1. Documentation Research: Use Context7 for latest dependency docs
     2. Svelte 5 Patterns: Consult Svelte LLM for modern runes patterns
     3. Queue Architecture: Build robust file queue management system
     4. Thumbnail System: PDF → Canvas → Image thumbnail pipeline
     5. Status Management: Real-time status updates with persistence
     6. Results Integration: Seamless file selection → results display
     7. Error Handling: Comprehensive error states with visual feedback
     8. Testing: All example invoices + queue management scenarios
     9. Polish: Smooth animations, responsive design, accessibility

     Success Criteria

     - Handle multiple PDF uploads with visual queue management
     - Generate readable PDF thumbnails for queue display
     - Process files sequentially with real-time status updates
     - Display comprehensive results with rescaled PDF preview
     - Robust error handling with retry capability
     - Clean, intuitive UI with professional queue management
