import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'vite';
import devtoolsJson from 'vite-plugin-devtools-json';

// Test configuration moved to vitest.config.ts to satisfy Vite type checking
export default defineConfig({
  plugins: [tailwindcss(), sveltekit(), devtoolsJson()],
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
  },
});
