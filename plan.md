## Invoice PDF Processing App – Technical Plan

Concise, actionable blueprint for implementing a SvelteKit app that ingests invoice PDFs, extracts structured data via Gemini 2.5 Flash (OpenRouter), and presents results with a multi-file processing queue.

---

## 1. Goals

1. Drag/drop multi-PDF upload with validation
2. Deterministic sequential processing queue with retry (backoff)
3. Hybrid PDF text extraction (native → OCR fallback)
4. AI-driven structured field extraction with confidence & provenance
5. Real‑time status + thumbnail previews
6. Clear error visibility & per‑file retry
7. Zero external persistence (in‑memory) for MVP; swappable storage layer

### Success Criteria

- < 5s perceived latency before first status update
- 100% of valid PDFs produce either Complete or Failed (no stuck state)
- Structured JSON output for: supplierName, invoiceNumber, invoiceDate, dueDate, totalAmount, vatAmount, currency, rawTextExcerpt
- Retry limit respected; failures logged with trace id
- Accessible UI (keyboard + aria labels for queue & results)

---

## 2. Scope

In scope: PDF upload, validation, extraction, AI parse, UI presentation, logging, queue control.
Out of scope (MVP): Auth, persistence DB, multi-user isolation, advanced NLP normalization, currency FX conversion.

---

## 3. High-Level Architecture

Browser (SvelteKit client) ↔ SvelteKit API routes ↔ External: OpenRouter (Gemini) & local PDF/OCR libs.

```
Client: Upload → /api/upload → file id
Queue store updates → /api/process (trigger) → pipeline
PDF text/thumbnail → pdf-processor & thumbnail-generator
AI extraction → gemini-client
Logs fetch → /api/logs
```

State held in memory (stores + module singleton). Designed so replacing with Redis/postgres just swaps queue/result adapter.

---

## 4. Data Models (TypeScript)

InvoiceCore
supplierName: string
invoiceNumber: string
invoiceDate: string (ISO)
dueDate?: string (ISO)
totalAmount: number
vatAmount?: number
currency?: string

ExtractedInvoice extends InvoiceCore
confidence: Record<string, number> // per-field 0..1
sourceModel: string
processingMs: number
rawTextExcerpt: string

QueueFile
id: string
name: string
size: number
uploadedAt: number
status: queued|processing|complete|failed|partial
attempts: number
error?: string
thumbnailUrl?: string
result?: ExtractedInvoice

ProcessingMeta
startedAt: number
endedAt?: number
retrySchedule: number[]

---

## 5. Status & UX Legend

| Status     | Icon | Color  | Definition                        |
| ---------- | ---- | ------ | --------------------------------- |
| queued     | 🕒   | gray   | Awaiting processor slot           |
| processing | 🔄   | blue   | Actively extracting/AI parsing    |
| complete   | ✅   | green  | Valid structured result stored    |
| failed     | ❌   | red    | Exhausted retries / fatal error   |
| partial    | ⚠️   | yellow | Low confidence threshold breached |

Low confidence threshold (field): < 0.6 default.

---

## 6. Processing Pipeline

1. Upload & Validate (size ≤10MB, mimetype application/pdf)
2. Queue Enqueue (status=queued)
3. Worker Loop (if idle + queued items)
4. Thumbnail (first page → canvas → data URL)
5. Text Extraction
   - Try PDF.js text content
   - If sparse / < N chars → fallback Tesseract OCR per page
6. AI Extraction (prompt w/ system + JSON schema hint)
7. Validation (Zod) → mark partial if passes with low confidence
8. Persist result in memory
9. Status finalization + log write

Retry Policy: Exponential backoff schedule (ms) e.g. `0,1500,4000`. Abort on validation unrecoverable errors (schema mismatch) or file unreadable.

---

## 7. Queue Logic

- Single active processor
- FIFO ordering; failed (retryable) reinsert head after backoff timer
- Idempotent guard: if status processing and worker crash (future enhancement) — current MVP ignores persistence after refresh
- Derived store exposes computed counts & progress

---

## 8. API Endpoints

| Method | Route            | Purpose                       | Body/Input                 | Response                |
| ------ | ---------------- | ----------------------------- | -------------------------- | ----------------------- |
| POST   | /api/upload      | Accept PDF                    | multipart/form-data (file) | { id }                  |
| POST   | /api/process     | Trigger queue tick (optional) | { id? }                    | { accepted: boolean }   |
| GET    | /api/logs        | Fetch recent logs             | query: limit?              | newline text / JSON TBD |
| GET    | /api/process?id= | Single item status            | id                         | QueueFile               |

Potential future: /api/health, /api/metrics.

---

## 9. Prompt & AI Strategy

- Provide concise raw extracted text (truncated to max tokens) + instruction
- Enforce JSON output via schema example
- Add bilingual hint (EN/NL) & currency detection
- Post-parse: attempt numeric normalization (comma vs dot)

Failure Modes:

- Non-JSON → attempt quick repair (regex bracket clamp) once
- Missing required fields → mark failed unless partial allowed

---

## 10. Dependencies

| Purpose    | Lib                     | Notes                                   |
| ---------- | ----------------------- | --------------------------------------- |
| PDF text   | pdf-parse OR pdfjs-dist | Evaluate bundle size; server route only |
| OCR        | tesseract.js            | Lazy load, worker pool size 1           |
| Thumbnails | canvas / pdfjs-dist     | Node canvas for server gen              |
| Validation | zod                     | Schema + refinement                     |
| AI client  | ai + @ai-sdk/openai     | OpenRouter access wrapper               |
| Logging    | winston                 | JSON + in-memory ring buffer            |

Add dev: vitest, @testing-library/svelte.

---

## 11. Logging & Observability

- Structured winston logger (level, ts, fileId, phase)
- In-memory ring buffer (e.g. 500 entries) served by /api/logs
- Attach traceId per file processing attempt
- Error categories: VALIDATION, OCR, AI_PARSE, NETWORK, SYSTEM

---

## 12. Error Handling Strategy

| Layer           | Error Type        | Action                         |
| --------------- | ----------------- | ------------------------------ |
| Upload          | Size/MIME invalid | 400 + reject                   |
| Text Extraction | PDF parse fail    | Retry (if transient) else fail |
| OCR             | Worker error      | Single retry then fail         |
| AI              | Network/parse     | Retry per schedule             |
| Validation      | Schema mismatch   | Fail (no further retries)      |

---

## 13. Testing Strategy

- Unit: utils (validators, queue manager, gemini-client stub)
- Component: FileDropzone, FileQueue, InvoiceResults, status icon states
- Integration: Simulated multi-file flow (fixtures in examples/invoices)
- Contract: AI response mocked to schema
- Snapshot: Prompt construction

---

## 14. Performance & Non-Functional

- Memory: Keep raw text truncated (first 50k chars)
- Concurrency: Single worker; future: configurable concurrency
- Security: Reject non-PDF, strip filename path segments, limit size
- Accessibility: Focus ring, role=list / listitem, aria-live for status
- Resilience: Retries with capped backoff; no infinite loops

---

## 15. Risks & Mitigations

| Risk                              | Impact         | Mitigation                                              |
| --------------------------------- | -------------- | ------------------------------------------------------- |
| Large scanned PDFs slow OCR       | High latency   | Early text length heuristic to skip OCR if already rich |
| AI inconsistent JSON              | Parse failures | Provide explicit JSON schema + repair attempt           |
| Memory growth with many files     | OOM            | Cap queue & purge oldest completed beyond N             |
| Canvas build issues (node-canvas) | Setup friction | Provide fallback: client-side thumbnail if server fails |
| Token overrun on large text       | Cost & failure | Truncate + approximate summarization chunk              |

---

## 16. Acceptance Criteria

1. Upload 5 mixed invoices → all reach terminal state (complete/failed) without manual refresh
2. Thumbnails appear within 2s of each enqueue (fast PDFs)
3. Failed AI call after max retries surfaces readable error + retry button
4. Partial status shown when any required field confidence < 0.6 but parse succeeded
5. Copy-to-clipboard button copies JSON result
6. Logs endpoint returns last ≥50 lines including at least one structured ERROR line

---

## 17. Implementation Order (Phased)

1. Types & validators
2. Queue + stores + worker skeleton
3. Upload route + client dropzone
4. PDF text + thumbnail utilities
5. AI client + prompt builder (mocked in tests)
6. Result rendering & status components
7. Retry + partial logic
8. Logging endpoints
9. Test suite & polish

---

## 18. Next Steps / Future Enhancements

- Persistent storage adapter (SQLite/Prisma)
- Multi-user sessions & auth
- Metrics dashboard (processing durations histogram)
- Currency normalization & line-item extraction
- Websocket status push (replace polling)

---

## 19. Env & Config

`.env` (example):

```
OPENROUTER_API_KEY=sk-...
AI_MODEL=google/gemini-2.5-flash
MAX_FILE_MB=10
LOG_RING_SIZE=500
RETRY_SCHEDULE=0,1500,4000
CONFIDENCE_LOW=0.6
```

---

## 20. Prompt Skeleton (Illustrative)

```
SYSTEM: You extract structured invoice fields. Output strict JSON matching provided schema.
USER: RAW_TEXT_START
<truncated invoice text>
RAW_TEXT_END

Return JSON:
{
  "supplierName": string,
  "invoiceNumber": string,
  "invoiceDate": ISO8601,
  "dueDate": ISO8601|null,
  "totalAmount": number,
  "vatAmount": number|null,
  "currency": string|null,
  "confidence": { field: 0-1 },
  "rawTextExcerpt": string
}
```

---

End of Plan.
