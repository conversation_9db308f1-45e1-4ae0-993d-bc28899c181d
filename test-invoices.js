#!/usr/bin/env bun
/**
 * Test script to validate invoice processing with example PDFs
 * Run with: bun test-invoices.js
 */

import { readdir, readFile } from 'node:fs/promises';
import { join } from 'node:path';

const EXAMPLES_DIR = './examples/invoices';
const API_BASE = 'http://localhost:5173/api';

// Test each example invoice
async function testInvoiceProcessing() {
  console.log('🧪 Starting Invoice Processing Tests...\n');

  try {
    // Get all PDF files from examples directory
    const files = await readdir(EXAMPLES_DIR);
    const pdfFiles = files.filter(file => file.endsWith('.pdf'));

    console.log(`📁 Found ${pdfFiles.length} example invoices:`);
    pdfFiles.forEach(file => console.log(`  • ${file}`));
    console.log();

    let successCount = 0;
    let errorCount = 0;

    for (const filename of pdfFiles) {
      console.log(`🔍 Processing: ${filename}`);

      try {
        const result = await processInvoice(filename);

        if (result.success) {
          console.log(`✅ SUCCESS: Extracted invoice data`);
          console.log(`   Supplier: ${result.data?.supplier || 'N/A'}`);
          console.log(`   Invoice ID: ${result.data?.invoiceId || 'N/A'}`);
          console.log(`   Total: ${result.data?.total || 'N/A'}`);
          console.log(
            `   Confidence: ${Math.round((result.metadata?.confidenceScore || 0) * 100)}%`,
          );
          console.log(`   Processing Time: ${result.metadata?.processingTime || 0}ms`);
          successCount++;
        } else {
          console.log(`❌ FAILED: ${result.error}`);
          errorCount++;
        }
      } catch (error) {
        console.log(`❌ ERROR: ${error.message}`);
        errorCount++;
      }

      console.log();
    }

    console.log('📊 Test Results Summary:');
    console.log(`  ✅ Successful: ${successCount}/${pdfFiles.length}`);
    console.log(`  ❌ Failed: ${errorCount}/${pdfFiles.length}`);
    console.log(`  🎯 Success Rate: ${Math.round((successCount / pdfFiles.length) * 100)}%`);
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  }
}

async function processInvoice(filename) {
  const filePath = join(EXAMPLES_DIR, filename);

  // Read the PDF file
  const fileBuffer = await readFile(filePath);
  const file = new File([fileBuffer], filename, { type: 'application/pdf' });

  // Step 1: Upload file
  const formData = new FormData();
  formData.append('file', file);

  const uploadResponse = await fetch(`${API_BASE}/upload`, {
    method: 'POST',
    body: formData,
  });

  const uploadResult = await uploadResponse.json();

  if (!uploadResult.success) {
    throw new Error(`Upload failed: ${uploadResult.error}`);
  }

  // Step 2: Process file
  const processResponse = await fetch(`${API_BASE}/process`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ fileId: uploadResult.fileId }),
  });

  const processResult = await processResponse.json();
  return processResult;
}

// Run tests if script is executed directly
if (import.meta.main) {
  testInvoiceProcessing().catch(console.error);
}
