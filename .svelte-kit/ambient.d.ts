
// this file is generated — do not edit it


/// <reference types="@sveltejs/kit" />

/**
 * Environment variables [loaded by Vite](https://vitejs.dev/guide/env-and-mode.html#env-files) from `.env` files and `process.env`. Like [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), this module cannot be imported into client-side code. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://svelte.dev/docs/kit/configuration#env) (if configured).
 * 
 * _Unlike_ [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), the values exported from this module are statically injected into your bundle at build time, enabling optimisations like dead code elimination.
 * 
 * ```ts
 * import { API_KEY } from '$env/static/private';
 * ```
 * 
 * Note that all environment variables referenced in your code should be declared (for example in an `.env` file), even if they don't have a value until the app is deployed:
 * 
 * ```
 * MY_FEATURE_FLAG=""
 * ```
 * 
 * You can override `.env` values from the command line like so:
 * 
 * ```sh
 * MY_FEATURE_FLAG="enabled" npm run dev
 * ```
 */
declare module '$env/static/private' {
	export const OPENROUTER_API_KEY: string;
	export const OPENROUTER_MODEL: string;
	export const SHELL: string;
	export const LSCOLORS: string;
	export const ITERM_PROFILE: string;
	export const COLORTERM: string;
	export const LESS: string;
	export const XPC_FLAGS: string;
	export const NVM_INC: string;
	export const TERM_PROGRAM_VERSION: string;
	export const JOBS: string;
	export const SCRIPTS_BIN_LOCAL: string;
	export const HISTSIZE: string;
	export const TERM_FEATURES: string;
	export const _P9K_TTY: string;
	export const NODE: string;
	export const __CFBundleIdentifier: string;
	export const SSH_AUTH_SOCK: string;
	export const P9K_TTY: string;
	export const ANDROID_SDK: string;
	export const TERM_SESSION_ID: string;
	export const npm_config_local_prefix: string;
	export const HOMEBREW_PREFIX: string;
	export const PWD: string;
	export const LOGNAME: string;
	export const PNPM_HOME: string;
	export const BUN_WHICH_IGNORE_CWD: string;
	export const LaunchInstanceID: string;
	export const BUN_BIN: string;
	export const _: string;
	export const FZF_DEFAULT_COMMAND: string;
	export const COMMAND_MODE: string;
	export const ITERM_SESSION_ID: string;
	export const HOME: string;
	export const LANG: string;
	export const LS_COLORS: string;
	export const npm_package_version: string;
	export const SECURITYSESSIONID: string;
	export const CONSOLE_NINJA: string;
	export const TMPDIR: string;
	export const LC_TERMINAL: string;
	export const CLICOLOR: string;
	export const INIT_CWD: string;
	export const INFOPATH: string;
	export const NVM_DIR: string;
	export const TERM: string;
	export const npm_package_name: string;
	export const ZSH: string;
	export const USER: string;
	export const COLORFGBG: string;
	export const HOMEBREW_CELLAR: string;
	export const LC_TERMINAL_VERSION: string;
	export const SHLVL: string;
	export const NVM_CD_FLAGS: string;
	export const ZPLUG_HOME: string;
	export const PAGER: string;
	export const HOMEBREW_REPOSITORY: string;
	export const _P9K_SSH_TTY: string;
	export const XPC_SERVICE_NAME: string;
	export const npm_config_user_agent: string;
	export const TERMINFO_DIRS: string;
	export const npm_execpath: string;
	export const SCRIPTS_BIN: string;
	export const npm_package_json: string;
	export const P9K_SSH: string;
	export const PATH: string;
	export const HISTORY_SUBSTRING_SEARCH_FUZZY: string;
	export const NVM_BIN: string;
	export const npm_node_execpath: string;
	export const OLDPWD: string;
	export const __CF_USER_TEXT_ENCODING: string;
	export const TERM_PROGRAM: string;
}

/**
 * Similar to [`$env/static/private`](https://svelte.dev/docs/kit/$env-static-private), except that it only includes environment variables that begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Values are replaced statically at build time.
 * 
 * ```ts
 * import { PUBLIC_BASE_URL } from '$env/static/public';
 * ```
 */
declare module '$env/static/public' {
	
}

/**
 * This module provides access to runtime environment variables, as defined by the platform you're running on. For example if you're using [`adapter-node`](https://github.com/sveltejs/kit/tree/main/packages/adapter-node) (or running [`vite preview`](https://svelte.dev/docs/kit/cli)), this is equivalent to `process.env`. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://svelte.dev/docs/kit/configuration#env) (if configured).
 * 
 * This module cannot be imported into client-side code.
 * 
 * Dynamic environment variables cannot be used during prerendering.
 * 
 * ```ts
 * import { env } from '$env/dynamic/private';
 * console.log(env.DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 * 
 * > In `dev`, `$env/dynamic` always includes environment variables from `.env`. In `prod`, this behavior will depend on your adapter.
 */
declare module '$env/dynamic/private' {
	export const env: {
		OPENROUTER_API_KEY: string;
		OPENROUTER_MODEL: string;
		SHELL: string;
		LSCOLORS: string;
		ITERM_PROFILE: string;
		COLORTERM: string;
		LESS: string;
		XPC_FLAGS: string;
		NVM_INC: string;
		TERM_PROGRAM_VERSION: string;
		JOBS: string;
		SCRIPTS_BIN_LOCAL: string;
		HISTSIZE: string;
		TERM_FEATURES: string;
		_P9K_TTY: string;
		NODE: string;
		__CFBundleIdentifier: string;
		SSH_AUTH_SOCK: string;
		P9K_TTY: string;
		ANDROID_SDK: string;
		TERM_SESSION_ID: string;
		npm_config_local_prefix: string;
		HOMEBREW_PREFIX: string;
		PWD: string;
		LOGNAME: string;
		PNPM_HOME: string;
		BUN_WHICH_IGNORE_CWD: string;
		LaunchInstanceID: string;
		BUN_BIN: string;
		_: string;
		FZF_DEFAULT_COMMAND: string;
		COMMAND_MODE: string;
		ITERM_SESSION_ID: string;
		HOME: string;
		LANG: string;
		LS_COLORS: string;
		npm_package_version: string;
		SECURITYSESSIONID: string;
		CONSOLE_NINJA: string;
		TMPDIR: string;
		LC_TERMINAL: string;
		CLICOLOR: string;
		INIT_CWD: string;
		INFOPATH: string;
		NVM_DIR: string;
		TERM: string;
		npm_package_name: string;
		ZSH: string;
		USER: string;
		COLORFGBG: string;
		HOMEBREW_CELLAR: string;
		LC_TERMINAL_VERSION: string;
		SHLVL: string;
		NVM_CD_FLAGS: string;
		ZPLUG_HOME: string;
		PAGER: string;
		HOMEBREW_REPOSITORY: string;
		_P9K_SSH_TTY: string;
		XPC_SERVICE_NAME: string;
		npm_config_user_agent: string;
		TERMINFO_DIRS: string;
		npm_execpath: string;
		SCRIPTS_BIN: string;
		npm_package_json: string;
		P9K_SSH: string;
		PATH: string;
		HISTORY_SUBSTRING_SEARCH_FUZZY: string;
		NVM_BIN: string;
		npm_node_execpath: string;
		OLDPWD: string;
		__CF_USER_TEXT_ENCODING: string;
		TERM_PROGRAM: string;
		[key: `PUBLIC_${string}`]: undefined;
		[key: `${string}`]: string | undefined;
	}
}

/**
 * Similar to [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), but only includes variables that begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Note that public dynamic environment variables must all be sent from the server to the client, causing larger network requests — when possible, use `$env/static/public` instead.
 * 
 * Dynamic environment variables cannot be used during prerendering.
 * 
 * ```ts
 * import { env } from '$env/dynamic/public';
 * console.log(env.PUBLIC_DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 */
declare module '$env/dynamic/public' {
	export const env: {
		[key: `PUBLIC_${string}`]: string | undefined;
	}
}
