type DynamicRoutes = {
	
};

type Layouts = {
	"/": undefined;
	"/api": undefined;
	"/api/logs": undefined;
	"/api/process": undefined;
	"/api/upload": undefined;
	"/countdown": undefined
};

export type RouteId = "/" | "/api" | "/api/logs" | "/api/process" | "/api/upload" | "/countdown";

export type RouteParams<T extends RouteId> = T extends keyof DynamicRoutes ? DynamicRoutes[T] : Record<string, never>;

export type LayoutParams<T extends RouteId> = Layouts[T] | Record<string, never>;

export type Pathname = "/" | "/api" | "/api/logs" | "/api/process" | "/api/upload" | "/countdown";

export type ResolvedPathname = `${"" | `/${string}`}${Pathname}`;

export type Asset = "/github.svg" | "/robots.txt";