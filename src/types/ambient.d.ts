// Ambient/quick-fix declarations to unblock strict TS compilation. Replace with proper types later.
declare module 'pdf-parse' {
  interface PDFInfo {
    numpages: number;
    numrender: number;
    info: Record<string, any>;
    metadata: any;
    version: string;
    text: string;
  }
  export function pdf(
    data: Buffer | Uint8Array | ArrayBuffer,
    options?: Record<string, any>,
  ): Promise<{ text: string } & PDFInfo>;
  export default pdf; // keep default for compatibility
}

// pdf2pic minimal types we actually use
declare module 'pdf2pic' {
  export interface ConverterOptions {
    density?: number;
    saveFilename?: string;
    savePath?: string;
    format?: 'png' | 'jpg' | 'jpeg';
    width?: number;
    height?: number;
  }
  export interface ConversionResult {
    path: string;
    page: number;
    name: string;
    size: number;
  }
  export type FromPathConverter = (page: number) => Promise<ConversionResult>;
  export function fromPath(path: string, options?: ConverterOptions): FromPathConverter;
  export function convert(buffer: Buffer, options?: ConverterOptions): Promise<ConversionResult[]>;
}

// Allow use of setInterval return type in Node + DOM context unifying number/Timeout mismatch
interface Window {
  setInterval: any;
}
