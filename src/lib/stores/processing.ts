import { writable, derived } from 'svelte/store';
import type { ErrorLogEntry } from '$lib/types/index.js';

// Traditional Svelte stores compatible with SSR
const _isProcessing = writable<boolean>(false);
const _currentFileId = writable<string | null>(null);
const _progressMessage = writable<string>('');
const _errorLogs = writable<ErrorLogEntry[]>([]);
const _showErrorPanel = writable<boolean>(false);

export const processingStore = {
  // Readable stores for components
  isProcessing: derived(_isProcessing, $isProcessing => $isProcessing),
  currentFileId: derived(_currentFileId, $currentFileId => $currentFileId),
  progressMessage: derived(_progressMessage, $progressMessage => $progressMessage),
  errorLogs: derived(_errorLogs, $errorLogs => $errorLogs),
  hasErrors: derived(_errorLogs, $errorLogs => $errorLogs.length > 0),
  recentErrors: derived(_errorLogs, $errorLogs =>
    $errorLogs.sort((a, b) => b.timestamp - a.timestamp).slice(0, 10),
  ),
  errorCount: derived(_errorLogs, $errorLogs => $errorLogs.length),
  isShowingErrorPanel: derived(_showErrorPanel, $showErrorPanel => $showErrorPanel),

  // Actions
  /**
   * Start processing a file
   */
  startProcessing(fileId: string, message: string = 'Processing...'): void {
    _isProcessing.set(true);
    _currentFileId.set(fileId);
    _progressMessage.set(message);
  },

  /**
   * Update processing progress message
   */
  updateProgress(message: string): void {
    _progressMessage.set(message);
  },

  /**
   * Complete processing
   */
  completeProcessing(): void {
    _isProcessing.set(false);
    _currentFileId.set(null);
    _progressMessage.set('');
  },

  /**
   * Add an error log entry
   */
  addError(
    fileId: string,
    fileName: string,
    error: string,
    stage: ErrorLogEntry['stage'],
    attemptNumber: number = 1,
  ): void {
    const errorEntry: ErrorLogEntry = {
      fileId,
      fileName,
      error,
      stage,
      attemptNumber,
      timestamp: Date.now(),
    };

    _errorLogs.update(logs => [...logs, errorEntry]);

    // Auto-show error panel if not already shown
    _showErrorPanel.update(shown => shown || true);
  },

  /**
   * Add success message (for completeness)
   */
  addSuccess(fileId: string, message: string): void {
    // Could be expanded to track successes if needed
    console.log(`Success for ${fileId}: ${message}`);
  },

  /**
   * Add multiple error log entries
   */
  addErrors(errors: ErrorLogEntry[]): void {
    _errorLogs.update(logs => [...logs, ...errors]);

    if (errors.length > 0) {
      _showErrorPanel.set(true);
    }
  },

  /**
   * Clear all error logs
   */
  clearErrors(): void {
    _errorLogs.set([]);
  },

  /**
   * Remove errors for a specific file
   */
  clearErrorsForFile(fileId: string): void {
    _errorLogs.update(logs => logs.filter(log => log.fileId !== fileId));
  },

  /**
   * Get errors for a specific file
   */
  getErrorsForFile(fileId: string): ErrorLogEntry[] {
    let currentLogs: ErrorLogEntry[] = [];
    _errorLogs.subscribe(logs => (currentLogs = logs))();
    return currentLogs.filter(log => log.fileId === fileId);
  },

  /**
   * Show error panel
   */
  showErrorPanel(): void {
    _showErrorPanel.set(true);
  },

  /**
   * Hide error panel
   */
  hideErrorPanel(): void {
    _showErrorPanel.set(false);
  },

  /**
   * Toggle error panel visibility
   */
  toggleErrorPanel(): void {
    _showErrorPanel.update(shown => !shown);
  },

  /**
   * Get error statistics by stage
   */
  getErrorStats(): Record<ErrorLogEntry['stage'], number> {
    let currentLogs: ErrorLogEntry[] = [];
    _errorLogs.subscribe(logs => (currentLogs = logs))();

    const stats = {
      upload: 0,
      thumbnail: 0,
      'text-extraction': 0,
      ocr: 0,
      'ai-processing': 0,
      processing: 0,
      network: 0,
    } as Record<ErrorLogEntry['stage'], number>;

    for (const error of currentLogs) {
      if (stats[error.stage] !== undefined) {
        stats[error.stage]++;
      }
    }

    return stats;
  },

  /**
   * Export error logs for external processing
   */
  exportErrorLogs(): ErrorLogEntry[] {
    let currentLogs: ErrorLogEntry[] = [];
    _errorLogs.subscribe(logs => (currentLogs = logs))();
    return JSON.parse(JSON.stringify(currentLogs));
  },

  /**
   * Import error logs from external source
   */
  importErrorLogs(logs: ErrorLogEntry[]): void {
    _errorLogs.set([...logs]);
  },

  /**
   * Subscribe to processing state changes
   */
  subscribe: _isProcessing.subscribe,
};
