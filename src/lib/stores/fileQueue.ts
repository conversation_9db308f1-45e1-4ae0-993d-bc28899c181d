import type { QueuedFile, QueueStats, ProcessingResult } from '$lib/types/index.js';
import { calculateQueueStats } from '$lib/types/index.js';

// Configurable retry schedule (ms) – could be moved to env/config
const RETRY_SCHEDULE = [0, 1500, 4000];
const LOW_CONFIDENCE_THRESHOLD = 0.6;

// Svelte 5 runes state management
let files = $state<QueuedFile[]>([]);
let processing = $state<boolean>(false);

export const fileQueueStore = {
  // Reactive getters using runes
  get files() {
    return files;
  },
  get processing() {
    return processing;
  },
  get stats() {
    return calculateQueueStats(files);
  },
  get hasFiles() {
    return files.length > 0;
  },
  get processingCount() {
    return files.filter(f => f.status === 'processing').length;
  },
  get completedCount() {
    return files.filter(f => f.status === 'completed').length;
  },
  get failedCount() {
    return files.filter(f => f.status === 'failed').length;
  },
  get isProcessing() {
    return processing;
  },

  // Actions
  /**
   * Add a new file to the queue
   */
  addFile(file: QueuedFile): void {
    files = [...files, { ...file, attempts: 0 }];
  },

  /**
   * Add multiple files to the queue
   */
  addFiles(newFiles: QueuedFile[]): void {
    files = [...files, ...newFiles];
  },

  /**
   * Remove a file from the queue
   */
  removeFile(fileId: string): void {
    files = files.filter(f => f.id !== fileId);
  },

  /**
   * Update a specific file in the queue
   */
  updateFile(fileId: string, updates: Partial<QueuedFile>): void {
    files = files.map(f => (f.id === fileId ? { ...f, ...updates } : f));
  },

  /**
   * Mark file as failed with error message
   */
  markAsFailed(fileId: string, error: string): void {
    files = files.map(f =>
      f.id === fileId
        ? {
            ...f,
            status: 'failed' as const,
            error,
            processingEndTime: Date.now(),
          }
        : f,
    );
  },

  /**
   * Start processing the next queued file
   */
  startNextProcessing(): QueuedFile | null {
    const queuedFile = files.find(f => f.status === 'queued');
    if (queuedFile) {
      files = files.map(f =>
        f.id === queuedFile.id
          ? {
              ...f,
              status: 'processing' as const,
              processingStartTime: Date.now(),
            }
          : f,
      );
      processing = true;
      return queuedFile;
    }
    return null;
  },

  /**
   * Complete processing for a file
   */
  completeProcessing(
    fileId: string,
    result: ProcessingResult,
    status: 'completed' | 'failed' | 'partial',
  ): void {
    files = files.map(f => {
      if (f.id !== fileId) return f;
      const base: QueuedFile = { ...f, processingEndTime: Date.now() };
      // Derive partial based on confidence if success
      let finalStatus = status;
      if (
        status === 'completed' &&
        result?.metadata?.confidenceScore !== undefined &&
        result.metadata.confidenceScore < LOW_CONFIDENCE_THRESHOLD
      ) {
        finalStatus = 'partial';
      }
      return {
        ...base,
        status: finalStatus,
        result,
        error: finalStatus === 'failed' ? result.error : undefined,
      };
    });
    processing = false;
    // Chain to next automatically
    this.processQueue();
  },

  /**
   * Retry processing for a failed file
   */
  retryFile(fileId: string): void {
    files = files.map(f =>
      f.id === fileId && f.status === 'failed'
        ? {
            ...f,
            status: 'queued' as const,
            error: undefined,
            processingStartTime: undefined,
            processingEndTime: undefined,
          }
        : f,
    );
    this.processQueue();
  },

  /**
   * Internal: schedule retry for a file if attempts remain
   */
  scheduleRetry(file: QueuedFile): void {
    const attempts = file.attempts ?? 0;
    if (attempts + 1 >= RETRY_SCHEDULE.length) {
      // Exhausted retries -> mark failed
      this.markAsFailed(file.id, file.error || 'Max retries exceeded');
      processing = false;
      this.processQueue();
      return;
    }
    const delay = RETRY_SCHEDULE[attempts + 1];
    setTimeout(() => {
      files = files.map(f => (f.id === file.id ? { ...f, status: 'queued', error: undefined } : f));
      processing = false;
      this.processQueue();
    }, delay);
  },

  /**
   * Public: kick off processing loop if idle
   */
  processQueue(): void {
    if (processing) return;
    const next = this.startNextProcessing();
    if (!next) return; // Nothing to do
    // Perform processing via API
    const fileIdToUse = next.serverFileId || next.id;
    fetch('/api/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fileId: fileIdToUse }),
    })
      .then(r => r.json())
      .then((result: ProcessingResult) => {
        // Increment attempts
        files = files.map(f => (f.id === next.id ? { ...f, attempts: (f.attempts ?? 0) + 1 } : f));
        if (result.success) {
          this.completeProcessing(next.id, result, 'completed');
        } else {
          files = files.map(f =>
            f.id === next.id ? { ...f, error: result.error, attempts: f.attempts ?? 0 } : f,
          );
          this.scheduleRetry({ ...next, error: result.error, attempts: next.attempts ?? 0 });
        }
      })
      .catch(err => {
        files = files.map(f =>
          f.id === next.id
            ? { ...f, error: err instanceof Error ? err.message : 'Processing error' }
            : f,
        );
        this.scheduleRetry({
          ...next,
          error: err instanceof Error ? err.message : 'Processing error',
          attempts: next.attempts ?? 0,
        });
      });
  },

  /**
   * Clear all processed files (completed and failed)
   */
  clearProcessed(): void {
    files = files.filter(f => !['completed', 'failed'].includes(f.status));
  },

  /**
   * Clear all files from the queue
   */
  clearAll(): void {
    files = [];
    processing = false;
  },

  /**
   * Get current files array (for non-reactive access)
   */
  getCurrentFiles(): QueuedFile[] {
    return files;
  },
};
