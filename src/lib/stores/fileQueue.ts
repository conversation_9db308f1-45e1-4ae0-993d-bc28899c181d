import { writable, derived } from 'svelte/store';
import type { QueuedFile, QueueStats, ProcessingResult } from '$lib/types/index.js';
import { calculateQueueStats } from '$lib/types/index.js';
import { processingStore } from './processing.js';

// Configurable retry schedule (ms) – could be moved to env/config
const RETRY_SCHEDULE = [0, 1500, 4000];
const LOW_CONFIDENCE_THRESHOLD = 0.6;

// Traditional Svelte stores compatible with SSR
const _files = writable<QueuedFile[]>([]);
const _processing = writable<boolean>(false);

export const fileQueueStore = {
  // Reactive stores for components
  files: derived(_files, $files => $files),
  processing: derived(_processing, $processing => $processing),
  stats: derived(_files, $files => calculateQueueStats($files)),
  hasFiles: derived(_files, $files => $files.length > 0),
  processingCount: derived(_files, $files => $files.filter(f => f.status === 'processing').length),
  completedCount: derived(_files, $files => $files.filter(f => f.status === 'completed').length),
  failedCount: derived(_files, $files => $files.filter(f => f.status === 'failed').length),
  isProcessing: derived(_processing, $processing => $processing),

  // Actions
  /**
   * Add a new file to the queue
   */
  addFile(file: QueuedFile): void {
    _files.update(files => [...files, { ...file, attempts: 0 }]);
    // Auto-trigger processing if queue is idle
    setTimeout(() => this.processQueue(), 100);
  },

  /**
   * Add multiple files to the queue
   */
  addFiles(newFiles: QueuedFile[]): void {
    _files.update(files => [...files, ...newFiles]);
  },

  /**
   * Remove a file from the queue
   */
  removeFile(fileId: string): void {
    _files.update(files => files.filter(f => f.id !== fileId));
  },

  /**
   * Update a specific file in the queue
   */
  updateFile(fileId: string, updates: Partial<QueuedFile>): void {
    _files.update(files => files.map(f => (f.id === fileId ? { ...f, ...updates } : f)));
    // Auto-trigger processing if a file was updated to queued status
    if (updates.status === 'queued' || (updates.serverFileId && !updates.status)) {
      setTimeout(() => this.processQueue(), 100);
    }
  },

  /**
   * Mark file as failed with error message
   */
  markAsFailed(fileId: string, error: string): void {
    _files.update(files =>
      files.map(f =>
        f.id === fileId
          ? {
              ...f,
              status: 'failed' as const,
              error,
              processingEndTime: Date.now(),
            }
          : f,
      ),
    );
  },

  /**
   * Start processing the next queued file
   */
  startNextProcessing(): QueuedFile | null {
    let currentFiles: QueuedFile[] = [];
    _files.subscribe(files => (currentFiles = files))();
    const queuedFile = currentFiles.find(f => f.status === 'queued');
    if (queuedFile) {
      _files.update(files =>
        files.map(f =>
          f.id === queuedFile.id
            ? {
                ...f,
                status: 'processing' as const,
                processingStartTime: Date.now(),
              }
            : f,
        ),
      );
      _processing.set(true);
      return queuedFile;
    }
    return null;
  },

  /**
   * Complete processing for a file
   */
  completeProcessing(
    fileId: string,
    result: ProcessingResult,
    status: 'completed' | 'failed' | 'partial',
  ): void {
    _files.update(files =>
      files.map(f => {
        if (f.id !== fileId) return f;
        const base: QueuedFile = { ...f, processingEndTime: Date.now() };
        // Derive partial based on confidence if success
        let finalStatus = status;
        if (
          status === 'completed' &&
          result?.metadata?.confidenceScore !== undefined &&
          result.metadata.confidenceScore < LOW_CONFIDENCE_THRESHOLD
        ) {
          finalStatus = 'partial';
        }
        return {
          ...base,
          status: finalStatus,
          result,
          error: finalStatus === 'failed' ? result.error : undefined,
        };
      }),
    );
    _processing.set(false);
    // Chain to next automatically
    this.processQueue();
  },

  /**
   * Retry processing for a failed file
   */
  retryFile(fileId: string): void {
    _files.update(files =>
      files.map(f =>
        f.id === fileId && f.status === 'failed'
          ? {
              ...f,
              status: 'queued' as const,
              error: undefined,
              processingStartTime: undefined,
              processingEndTime: undefined,
            }
          : f,
      ),
    );
    this.processQueue();
  },

  /**
   * Internal: schedule retry for a file if attempts remain
   */
  scheduleRetry(file: QueuedFile): void {
    const attempts = file.attempts ?? 0;
    if (attempts + 1 >= RETRY_SCHEDULE.length) {
      // Exhausted retries -> mark failed
      this.markAsFailed(file.id, file.error || 'Max retries exceeded');
      _processing.set(false);
      this.processQueue();
      return;
    }
    const delay = RETRY_SCHEDULE[attempts + 1];
    setTimeout(() => {
      _files.update(files =>
        files.map(f => (f.id === file.id ? { ...f, status: 'queued', error: undefined } : f)),
      );
      _processing.set(false);
      this.processQueue();
    }, delay);
  },

  /**
   * Public: kick off processing loop if idle
   */
  processQueue(): void {
    let currentProcessing = false;
    _processing.subscribe(p => (currentProcessing = p))();
    if (currentProcessing) return;
    const next = this.startNextProcessing();
    if (!next) return; // Nothing to do

    // Update processing store for UI feedback
    processingStore.startProcessing(next.id, 'Processing file...');

    // Perform processing via API
    const fileIdToUse = next.serverFileId || next.id;
    fetch('/api/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ fileId: fileIdToUse }),
    })
      .then(r => r.json())
      .then((result: ProcessingResult) => {
        // Increment attempts
        _files.update(files =>
          files.map(f => (f.id === next.id ? { ...f, attempts: (f.attempts ?? 0) + 1 } : f)),
        );
        if (result.success) {
          this.completeProcessing(next.id, result, 'completed');
          processingStore.addSuccess(next.id, 'Processing completed successfully');
        } else {
          _files.update(files =>
            files.map(f =>
              f.id === next.id ? { ...f, error: result.error, attempts: f.attempts ?? 0 } : f,
            ),
          );
          processingStore.addError(
            next.id,
            next.name,
            result.error || 'Processing failed',
            'processing',
          );
          this.scheduleRetry({ ...next, error: result.error, attempts: next.attempts ?? 0 });
        }
        processingStore.completeProcessing();
      })
      .catch(err => {
        const errorMessage = err instanceof Error ? err.message : 'Processing error';
        _files.update(files =>
          files.map(f => (f.id === next.id ? { ...f, error: errorMessage } : f)),
        );
        processingStore.addError(next.id, next.name, errorMessage, 'network');
        processingStore.completeProcessing();
        this.scheduleRetry({
          ...next,
          error: errorMessage,
          attempts: next.attempts ?? 0,
        });
      });
  },

  /**
   * Clear all processed files (completed and failed)
   */
  clearProcessed(): void {
    _files.update(files => files.filter(f => !['completed', 'failed'].includes(f.status)));
  },

  /**
   * Clear all files from the queue
   */
  clearAll(): void {
    _files.set([]);
    _processing.set(false);
  },

  /**
   * Get current files array (for non-reactive access)
   */
  getCurrentFiles(): QueuedFile[] {
    let currentFiles: QueuedFile[] = [];
    _files.subscribe(files => (currentFiles = files))();
    return currentFiles;
  },
};
