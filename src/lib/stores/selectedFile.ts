import { writable, derived } from 'svelte/store';
import type { QueuedFile } from '$lib/types/index.js';

// Traditional Svelte stores compatible with SSR
const _selectedFile = writable<QueuedFile | null>(null);
const _showingResults = writable<boolean>(false);

export const selectedFileStore = {
  // Reactive stores for components
  selectedFile: derived(_selectedFile, $selectedFile => $selectedFile),
  showingResults: derived(_showingResults, $showingResults => $showingResults),
  hasSelection: derived(_selectedFile, $selectedFile => $selectedFile !== null),
  isShowingResults: derived(_showingResults, $showingResults => $showingResults),
  canShowResults: derived(
    _selectedFile,
    $selectedFile =>
      $selectedFile !== null && ['completed', 'failed', 'partial'].includes($selectedFile.status),
  ),

  // Actions
  /**
   * Select a file from the queue
   */
  selectFile(file: QueuedFile | null): void {
    _selectedFile.set(file);
    const shouldShowResults =
      file !== null && ['completed', 'failed', 'partial'].includes(file.status);
    _showingResults.set(shouldShowResults);
  },

  /**
   * Clear the selected file
   */
  clearSelection(): void {
    _selectedFile.set(null);
    _showingResults.set(false);
  },

  /**
   * Update the currently selected file (if it matches the ID)
   */
  updateSelectedFile(fileId: string, updates: Partial<QueuedFile>): void {
    _selectedFile.update(selectedFile => {
      if (selectedFile && selectedFile.id === fileId) {
        const updatedFile = { ...selectedFile, ...updates };

        // Update results view state if status changed
        if (updates.status) {
          const shouldShowResults = ['completed', 'failed', 'partial'].includes(updates.status);
          _showingResults.set(shouldShowResults);
        }

        return updatedFile;
      }
      return selectedFile;
    });
  },

  /**
   * Show results view for the selected file
   */
  showResults(): void {
    _selectedFile.update(currentFile => {
      if (currentFile && ['completed', 'failed', 'partial'].includes(currentFile.status)) {
        _showingResults.set(true);
      }
      return currentFile;
    });
  },

  /**
   * Hide results view
   */
  hideResults(): void {
    _showingResults.set(false);
  },

  /**
   * Toggle results view
   */
  toggleResults(): void {
    _selectedFile.update(currentFile => {
      if (currentFile && ['completed', 'failed', 'partial'].includes(currentFile.status)) {
        _showingResults.update(showing => !showing);
      }
      return currentFile;
    });
  },
};
