import type { QueuedFile } from '$lib/types/index.js';

// Svelte 5 runes state management
let selectedFile = $state<QueuedFile | null>(null);
let showingResults = $state<boolean>(false);

export const selectedFileStore = {
  // Reactive getters using runes
  get selectedFile() {
    return selectedFile;
  },
  get showingResults() {
    return showingResults;
  },
  get hasSelection() {
    return selectedFile !== null;
  },
  get isShowingResults() {
    return showingResults;
  },
  get canShowResults() {
    return (
      selectedFile !== null && ['completed', 'failed', 'partial'].includes(selectedFile.status)
    );
  },

  // Actions
  /**
   * Select a file from the queue
   */
  selectFile(file: QueuedFile | null): void {
    selectedFile = file;
    const shouldShowResults =
      file !== null && ['completed', 'failed', 'partial'].includes(file.status);
    showingResults = shouldShowResults;
  },

  /**
   * Clear the selected file
   */
  clearSelection(): void {
    selectedFile = null;
    showingResults = false;
  },

  /**
   * Update the currently selected file (if it matches the ID)
   */
  updateSelectedFile(fileId: string, updates: Partial<QueuedFile>): void {
    if (selectedFile && selectedFile.id === fileId) {
      const updatedFile = { ...selectedFile, ...updates };

      // Update results view state if status changed
      if (updates.status) {
        const shouldShowResults = ['completed', 'failed', 'partial'].includes(updates.status);
        showingResults = shouldShowResults;
      }

      selectedFile = updatedFile;
    }
  },

  /**
   * Show results view for the selected file
   */
  showResults(): void {
    if (selectedFile && ['completed', 'failed', 'partial'].includes(selectedFile.status)) {
      showingResults = true;
    }
  },

  /**
   * Hide results view
   */
  hideResults(): void {
    showingResults = false;
  },

  /**
   * Toggle results view
   */
  toggleResults(): void {
    if (selectedFile && ['completed', 'failed', 'partial'].includes(selectedFile.status)) {
      showingResults = !showingResults;
    }
  },
};
