import pdf from 'pdf-parse';
import { fromPath } from 'pdf2pic';
import { createWorker } from 'tesseract.js';
import type { ProcessingMetadata } from '$lib/types/index.js';

export interface TextExtractionResult {
  text: string;
  method: 'text' | 'ocr';
  metadata: Partial<ProcessingMetadata>;
}

/**
 * Extract text from PDF using pdf-parse library
 * This is fast but only works for PDFs with extractable text
 */
export async function extractTextFromPDF(buffer: Buffer): Promise<string> {
  try {
    const data = await pdf(buffer);
    return data.text;
  } catch (error) {
    throw new Error(
      `PDF text extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Extract text using OCR via Tesseract.js
 * This is slower but works with scanned/image-based PDFs
 */
export async function extractTextWithOCR(buffer: Buffer): Promise<string> {
  const startTime = Date.now();

  try {
    // We'll create a converter after writing temp file (pdf2pic expects a file path)

    // Save buffer to temporary file for pdf2pic
    const fs = await import('fs');
    const path = await import('path');
    const tempDir = '/tmp';
    const tempPdfPath = path.join(tempDir, `temp-${Date.now()}.pdf`);

    await fs.promises.writeFile(tempPdfPath, buffer);

    try {
      // Create converter bound to temp file
      const convert = fromPath(tempPdfPath, {
        density: 200,
        saveFilename: 'page',
        savePath: '/tmp',
        format: 'png',
        width: 2000,
        height: 2800,
      });
      // Convert only first page (returns single result object)
      const firstPage = await convert(1);
      if (!firstPage || !firstPage.path) {
        throw new Error('Failed to convert PDF to image');
      }

      const imagePath = firstPage.path;

      // Perform OCR on the image
      const worker = await createWorker('eng');

      try {
        const {
          data: { text },
        } = await worker.recognize(imagePath);
        return text;
      } finally {
        await worker.terminate();
      }
    } finally {
      // Cleanup temporary files
      await fs.promises.unlink(tempPdfPath).catch(() => {});
    }
  } catch (error) {
    throw new Error(
      `OCR extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Main function to extract text from PDF with automatic fallback to OCR
 */
export async function processPDF(buffer: Buffer): Promise<TextExtractionResult> {
  const startTime = Date.now();

  // First, try direct text extraction
  try {
    const text = await extractTextFromPDF(buffer);

    // Check if we got meaningful text (not just whitespace/special chars)
    const meaningfulText = text.trim().replace(/\s+/g, ' ');

    if (meaningfulText.length > 50) {
      return {
        text: meaningfulText,
        method: 'text',
        metadata: {
          method: 'text',
          processingTime: Date.now() - startTime,
          attemptCount: 1,
          confidenceScore: 0.95, // High confidence for direct text extraction
        },
      };
    } else {
      console.log('Text extraction returned insufficient content, falling back to OCR');
    }
  } catch (error) {
    console.log('Text extraction failed, falling back to OCR:', error);
  }

  // Fallback to OCR
  try {
    const text = await extractTextWithOCR(buffer);
    const meaningfulText = text.trim().replace(/\s+/g, ' ');

    return {
      text: meaningfulText,
      method: 'ocr',
      metadata: {
        method: 'ocr',
        processingTime: Date.now() - startTime,
        attemptCount: 2,
        confidenceScore: 0.75, // Lower confidence for OCR
      },
    };
  } catch (error) {
    throw new Error(
      `Both text extraction and OCR failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Validate PDF file
 */
export function validatePDF(buffer: Buffer): { valid: boolean; error?: string } {
  // Check PDF magic number
  const header = buffer.subarray(0, 4).toString();
  if (header !== '%PDF') {
    return { valid: false, error: 'Invalid PDF file format' };
  }

  // Basic size check
  if (buffer.length < 1000) {
    return { valid: false, error: 'PDF file appears to be too small or corrupted' };
  }

  return { valid: true };
}
