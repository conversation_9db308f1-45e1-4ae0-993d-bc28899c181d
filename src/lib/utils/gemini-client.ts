import { OpenAI } from 'openai';
import type { InvoiceData } from '$lib/types/index.js';
import { InvoiceDataSchema } from '$lib/types/index.js';
import { OPENROUTER_API_KEY } from '$env/static/private';

export interface AIExtractionResult {
  success: boolean;
  data?: InvoiceData;
  confidenceScore: number;
  error?: string;
  tokensUsed?: number;
}

export interface AIExtractionOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  maxRetries?: number;
}

const DEFAULT_OPTIONS: Required<AIExtractionOptions> = {
  model: 'google/gemini-flash-1.5',
  maxTokens: 2000,
  temperature: 0.1,
  maxRetries: 1,
};

/**
 * Create OpenRouter client for Gemini 2.5 Flash
 */
function createOpenRouterClient(): OpenAI {
  if (!OPENROUTER_API_KEY) {
    throw new Error('OPENROUTER_API_KEY environment variable is required');
  }

  return new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey: OPENROUTER_API_KEY,
    defaultHeaders: {
      'HTTP-Referer': 'http://localhost:5173', // Your site URL
      'X-Title': 'Invoice Processing App', // Your site name
    },
  });
}

/**
 * Create the prompt for invoice data extraction
 */
function createExtractionPrompt(text: string, language: 'auto' | 'en' | 'nl' = 'auto'): string {
  const languageInstruction =
    language === 'auto'
      ? 'The invoice text may be in Dutch or English.'
      : language === 'nl'
        ? 'The invoice text is in Dutch.'
        : 'The invoice text is in English.';

  return `You are an expert at extracting structured data from invoice text. ${languageInstruction}

Extract the following information from the invoice text and return it as a JSON object:

{
  "supplier": "Company or organization name that issued the invoice",
  "invoiceId": "Invoice number or ID (like 'INV-2024-001', 'BUCKEUR0000272352', etc.)",
  "invoiceDate": "Date when the invoice was issued (format: YYYY-MM-DD if possible, otherwise keep original format)",
  "dueDate": "Payment due date (format: YYYY-MM-DD if possible, otherwise keep original format)",
  "total": "Total amount including VAT/BTW (with currency symbol, e.g., '€234.91', '$180.00')",
  "vatTotal": "VAT/BTW amount only (with currency symbol, e.g., '€40.77', '$0.00' if no VAT)"
}

Important extraction rules:
1. For supplier: Use the company name from the sender/billing entity, not the recipient
2. For invoiceId: Look for terms like "Invoice number", "Factuurnummer", "Invoice ID", "Reference", etc.
3. For dates: Convert to YYYY-MM-DD if the format is clear, otherwise preserve the original format
4. For amounts: Include currency symbols and preserve the exact format shown
5. For VAT: Look for "VAT", "BTW", "Tax", or similar terms. If no VAT is shown, use "€0.00" or appropriate currency
6. If any field cannot be found, use "Not found" as the value
7. Be precise - don't guess or make up information

Here is the invoice text to process:

${text}

Return only the JSON object, no additional text or explanation.`;
}

/**
 * Parse and validate the AI response
 */
function parseAIResponse(response: string): { data?: InvoiceData; error?: string } {
  try {
    // Try to extract JSON from response
    let jsonStr = response.trim();

    // Handle cases where AI adds extra text around the JSON
    const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonStr = jsonMatch[0];
    }

    const parsed = JSON.parse(jsonStr);

    // Validate using Zod schema
    const result = InvoiceDataSchema.safeParse(parsed);

    if (result.success) {
      return { data: result.data };
    } else {
      return { error: `Validation failed: ${result.error.message}` };
    }
  } catch (error) {
    return {
      error: `Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Calculate confidence score based on the extracted data
 */
function calculateConfidenceScore(data: InvoiceData): number {
  let score = 0;
  const fields = Object.values(data);
  const totalFields = fields.length;

  // Basic presence check (50% of score)
  const validFields = fields.filter(
    value => value && value !== 'Not found' && value.trim().length > 0,
  );
  score += (validFields.length / totalFields) * 0.5;

  // Quality checks (50% of score)
  let qualityScore = 0;

  // Supplier name quality
  if (data.supplier && data.supplier !== 'Not found' && data.supplier.length > 2) {
    qualityScore += 0.15;
  }

  // Invoice ID quality (should look like an ID)
  if (data.invoiceId && data.invoiceId !== 'Not found') {
    const hasIdPattern = /[A-Z0-9\-_]{3,}/.test(data.invoiceId);
    if (hasIdPattern) qualityScore += 0.15;
  }

  // Date format quality
  const datePattern = /\d{4}-\d{2}-\d{2}|\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/;
  if (datePattern.test(data.invoiceDate)) qualityScore += 0.1;
  if (datePattern.test(data.dueDate)) qualityScore += 0.1;

  // Amount format quality (should have currency symbol)
  const amountPattern = /[€$£¥]\s*\d+(?:[.,]\d{2})?|\d+(?:[.,]\d{2})?\s*[€$£¥]/;
  if (amountPattern.test(data.total)) qualityScore += 0.15;
  if (amountPattern.test(data.vatTotal)) qualityScore += 0.1;

  score += qualityScore;

  return Math.min(score, 1);
}

/**
 * Extract invoice data from text using Gemini 2.5 Flash via OpenRouter
 */
export async function extractInvoiceData(
  text: string,
  options: AIExtractionOptions = {},
): Promise<AIExtractionResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const client = createOpenRouterClient();

  if (!text.trim()) {
    return {
      success: false,
      confidenceScore: 0,
      error: 'No text provided for extraction',
    };
  }

  let lastError: string | undefined;

  // Retry logic
  for (let attempt = 1; attempt <= opts.maxRetries + 1; attempt++) {
    try {
      const prompt = createExtractionPrompt(text);

      const response = await client.chat.completions.create({
        model: opts.model,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: opts.maxTokens,
        temperature: opts.temperature,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response content from AI');
      }

      const parseResult = parseAIResponse(content);
      if (parseResult.error) {
        throw new Error(parseResult.error);
      }

      if (!parseResult.data) {
        throw new Error('No data extracted from response');
      }

      const confidenceScore = calculateConfidenceScore(parseResult.data);

      return {
        success: true,
        data: parseResult.data,
        confidenceScore,
        tokensUsed: response.usage?.total_tokens,
      };
    } catch (error) {
      lastError = error instanceof Error ? error.message : 'Unknown error';
      console.log(`AI extraction attempt ${attempt} failed:`, lastError);

      if (attempt <= opts.maxRetries) {
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
      }
    }
  }

  return {
    success: false,
    confidenceScore: 0,
    error: `AI extraction failed after ${opts.maxRetries + 1} attempts. Last error: ${lastError}`,
  };
}

/**
 * Test the AI extraction with a sample text
 */
export async function testAIExtraction(): Promise<AIExtractionResult> {
  const sampleText = `
		Invoice
		Invoice number TVHUK09C-0001
		Date of issue July 29, 2025
		Date due July 29, 2025
		Anthropic, PBC
		548 Market Street
		San Francisco, California 94104
		€180.00 due July 29, 2025
		Subtotal €180.00
		Total €180.00
		Amount due €180.00
	`;

  return await extractInvoiceData(sampleText);
}
