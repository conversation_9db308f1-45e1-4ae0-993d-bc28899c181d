import { z } from 'zod';

// File upload validation
export const FileUploadSchema = z.object({
  name: z.string().min(1, 'Filename is required'),
  size: z.number().positive('File size must be positive'),
  type: z.string().min(1, 'File type is required'),
});

export interface FileValidationOptions {
  maxSize: number;
  allowedTypes: string[];
  allowedExtensions: string[];
}

export const DEFAULT_FILE_OPTIONS: FileValidationOptions = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['application/pdf'],
  allowedExtensions: ['.pdf'],
};

export interface FileValidationResult {
  valid: boolean;
  error?: string;
  warnings: string[]; // always array (may be empty) to avoid undefined union issues
}

/**
 * Validate an uploaded file
 */
export function validateFile(
  file: File,
  options: Partial<FileValidationOptions> = {},
): FileValidationResult {
  const opts = { ...DEFAULT_FILE_OPTIONS, ...options };
  const warnings: string[] = [];

  // Check file size
  if (file.size > opts.maxSize) {
    return {
      valid: false,
      error: `File size (${formatBytes(file.size)}) exceeds maximum allowed size (${formatBytes(opts.maxSize)})`,
      warnings: [],
    };
  }

  // Check file type
  if (!opts.allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File type "${file.type}" is not allowed. Allowed types: ${opts.allowedTypes.join(', ')}`,
      warnings: [],
    };
  }

  // Check file extension
  const extension = getFileExtension(file.name);
  if (!opts.allowedExtensions.includes(extension)) {
    return {
      valid: false,
      error: `File extension "${extension}" is not allowed. Allowed extensions: ${opts.allowedExtensions.join(', ')}`,
      warnings: [],
    };
  }

  // Check filename
  if (file.name.length > 255) {
    warnings.push('Filename is very long and may be truncated');
  }

  // Check for potentially problematic characters
  if (/[<>:"/\\|?*]/.test(file.name)) {
    warnings.push('Filename contains characters that may cause issues');
  }

  // Size warnings
  if (file.size < 1000) {
    warnings.push('File is very small and may be empty or corrupted');
  }

  if (file.size > 5 * 1024 * 1024) {
    warnings.push('Large file size may result in slower processing');
  }

  return {
    valid: true,
    warnings,
  };
}

/**
 * Validate file content (PDF magic number)
 */
export function validatePDFContent(buffer: ArrayBuffer): FileValidationResult {
  const uint8Array = new Uint8Array(buffer);
  // Check PDF magic number
  const header = String.fromCharCode(...uint8Array.slice(0, 4));
  if (header !== '%PDF') {
    return {
      valid: false,
      error: 'File does not appear to be a valid PDF (missing PDF header)',
      warnings: [],
    };
  }
  // Check minimum size
  if (buffer.byteLength < 1000) {
    return { valid: false, error: 'PDF file is too small and may be corrupted', warnings: [] };
  }
  // Look for PDF trailer near end
  const content = String.fromCharCode(...uint8Array.slice(-1000));
  if (!content.includes('%%EOF')) {
    return {
      valid: false,
      error: 'PDF file appears to be incomplete (missing end marker)',
      warnings: [],
    };
  }
  return { valid: true, warnings: [] };
}

/**
 * Sanitize filename for safe storage
 */
export function sanitizeFilename(filename: string): string {
  // Remove path separators and dangerous characters
  let sanitized = filename.replace(/[<>:"/\\|?*]/g, '_');

  // Remove control characters
  sanitized = sanitized.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

  // Limit length
  if (sanitized.length > 255) {
    const extension = getFileExtension(sanitized);
    const nameWithoutExt = sanitized.slice(0, sanitized.length - extension.length);
    sanitized = nameWithoutExt.slice(0, 255 - extension.length) + extension;
  }

  // Ensure it's not empty
  if (!sanitized.trim()) {
    sanitized = 'unnamed_file.pdf';
  }

  return sanitized;
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.');
  if (lastDot === -1) return '';
  return filename.slice(lastDot).toLowerCase();
}

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Generate a unique file ID
 */
export function generateFileId(): string {
  return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate email address
 */
export const EmailSchema = z.string().email('Invalid email address');

/**
 * Validate URL
 */
export const URLSchema = z.string().url('Invalid URL format');

/**
 * Validate date string
 */
export function validateDate(dateString: string): { valid: boolean; error?: string } {
  // Try common date formats
  const formats = [
    /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
    /^\d{2}[\/\-\.]\d{2}[\/\-\.]\d{4}$/, // DD/MM/YYYY or DD-MM-YYYY
    /^\d{2}[\/\-\.]\d{2}[\/\-\.]\d{2}$/, // DD/MM/YY
    /^\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{4}$/, // D/M/YYYY
  ];

  const hasValidFormat = formats.some(format => format.test(dateString));
  if (!hasValidFormat) {
    return { valid: false, error: 'Invalid date format' };
  }

  // Try to parse as Date
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return { valid: false, error: 'Invalid date value' };
  }

  // Check for reasonable date range (not too far in past or future)
  const now = new Date();
  const minDate = new Date(now.getFullYear() - 50, 0, 1); // 50 years ago
  const maxDate = new Date(now.getFullYear() + 10, 11, 31); // 10 years from now

  if (date < minDate || date > maxDate) {
    return { valid: false, error: 'Date is outside reasonable range' };
  }

  return { valid: true };
}
