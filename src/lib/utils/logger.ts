import type { ErrorLogEntry } from '$lib/types/index.js';

export interface LoggerOptions {
  logFile: string;
  maxLogSize: number; // Max size in bytes
  maxLogs: number; // Max number of log entries
}

const DEFAULT_OPTIONS: LoggerOptions = {
  logFile: 'logs/processing.log',
  maxLogSize: 10 * 1024 * 1024, // 10MB
  maxLogs: 1000,
};

/**
 * Logger class for handling error logs
 */
export class Logger {
  private options: LoggerOptions;

  constructor(options: Partial<LoggerOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  /**
   * Log an error entry
   */
  async logError(entry: Omit<ErrorLogEntry, 'timestamp'>): Promise<void> {
    const logEntry: ErrorLogEntry = {
      ...entry,
      timestamp: Date.now(),
    };

    try {
      await this.writeLogEntry(logEntry);
    } catch (error) {
      console.error('Failed to write log entry:', error);
      // Don't throw - logging failures shouldn't break the app
    }
  }

  /**
   * Read all log entries
   */
  async readLogs(): Promise<ErrorLogEntry[]> {
    try {
      const fs = await import('fs');
      const path = await import('path');

      const logPath = this.getLogPath();

      if (!(await this.fileExists(logPath))) {
        return [];
      }

      const content = await fs.promises.readFile(logPath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());

      const entries: ErrorLogEntry[] = [];
      for (const line of lines) {
        try {
          const entry = JSON.parse(line);
          entries.push(entry);
        } catch {
          // Skip invalid lines
        }
      }

      return entries.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to read logs:', error);
      return [];
    }
  }

  /**
   * Get logs for a specific file ID
   */
  async getLogsForFile(fileId: string): Promise<ErrorLogEntry[]> {
    const allLogs = await this.readLogs();
    return allLogs.filter(log => log.fileId === fileId);
  }

  /**
   * Get recent logs (last N entries)
   */
  async getRecentLogs(count: number = 50): Promise<ErrorLogEntry[]> {
    const allLogs = await this.readLogs();
    return allLogs.slice(0, count);
  }

  /**
   * Clear all logs
   */
  async clearLogs(): Promise<void> {
    try {
      const fs = await import('fs');
      const logPath = this.getLogPath();

      if (await this.fileExists(logPath)) {
        await fs.promises.unlink(logPath);
      }
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  }

  /**
   * Rotate logs if they exceed size limits
   */
  private async rotateLogsIfNeeded(): Promise<void> {
    try {
      const fs = await import('fs');
      const logPath = this.getLogPath();

      if (!(await this.fileExists(logPath))) {
        return;
      }

      const stats = await fs.promises.stat(logPath);

      if (stats.size > this.options.maxLogSize) {
        // Keep only the most recent entries
        const logs = await this.readLogs();
        const recentLogs = logs.slice(0, Math.floor(this.options.maxLogs / 2));

        // Rewrite the log file with recent entries
        const content = recentLogs.map(log => JSON.stringify(log)).join('\n') + '\n';
        await fs.promises.writeFile(logPath, content);
      }
    } catch (error) {
      console.error('Failed to rotate logs:', error);
    }
  }

  /**
   * Write a single log entry to the file
   */
  private async writeLogEntry(entry: ErrorLogEntry): Promise<void> {
    const fs = await import('fs');
    const logPath = this.getLogPath();

    // Ensure directory exists
    await this.ensureDirectoryExists();

    // Rotate logs if needed before writing
    await this.rotateLogsIfNeeded();

    const logLine = JSON.stringify(entry) + '\n';
    await fs.promises.appendFile(logPath, logLine);
  }

  /**
   * Ensure the log directory exists
   */
  private async ensureDirectoryExists(): Promise<void> {
    const fs = await import('fs');
    const path = await import('path');

    const logPath = this.getLogPath();
    const logDir = path.dirname(logPath);

    try {
      await fs.promises.mkdir(logDir, { recursive: true });
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  /**
   * Check if a file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      const fs = await import('fs');
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get the full log file path
   */
  private getLogPath(): string {
    const path = require('path');
    return path.resolve(this.options.logFile);
  }
}

// Default logger instance
export const logger = new Logger();

// Convenience functions
export async function logError(
  fileId: string,
  fileName: string,
  error: string,
  stage: ErrorLogEntry['stage'],
  attemptNumber: number = 1,
  metadata?: Record<string, unknown>,
): Promise<void> {
  await logger.logError({
    fileId,
    fileName,
    error,
    stage,
    attemptNumber,
    metadata: metadata || {},
  });
}

export async function getLogs(): Promise<ErrorLogEntry[]> {
  return await logger.readLogs();
}

export async function getLogsForFile(fileId: string): Promise<ErrorLogEntry[]> {
  return await logger.getLogsForFile(fileId);
}
