import { fromPath } from 'pdf2pic';
import sharp from 'sharp';

export interface ThumbnailOptions {
  width: number;
  height: number;
  quality: number;
}

export interface ThumbnailResult {
  success: boolean;
  thumbnail?: string; // Base64 encoded image
  error?: string;
}

const DEFAULT_OPTIONS: ThumbnailOptions = {
  width: 200,
  height: 260,
  quality: 80,
};

/**
 * Generate a thumbnail from the first page of a PDF
 */
export async function generatePDFThumbnail(
  buffer: Buffer,
  options: Partial<ThumbnailOptions> = {},
): Promise<ThumbnailResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  try {
    const fs = await import('fs');
    const path = await import('path');

    // Create temporary directory and file paths
    const tempDir = '/tmp';
    const tempId = `pdf-thumb-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const tempPdfPath = path.join(tempDir, `${tempId}.pdf`);

    // Write buffer to temporary PDF file
    await fs.promises.writeFile(tempPdfPath, buffer);

    try {
      // Configure pdf2pic converter
      const convert = fromPath(tempPdfPath, {
        density: 150, // DPI for good quality but reasonable size
        saveFilename: 'page',
        savePath: tempDir,
        format: 'png',
        width: opts.width * 2, // Higher resolution for better quality after resize
        height: opts.height * 2,
      });

      // Convert first page to image (single result)
      const firstPage = await convert(1);
      if (!firstPage || !firstPage.path) {
        throw new Error('Failed to convert PDF page to image');
      }
      const imagePath = firstPage.path;

      try {
        // Process image with sharp for optimization
        const processedImageBuffer = await sharp(imagePath)
          .resize(opts.width, opts.height, {
            fit: 'contain',
            background: { r: 255, g: 255, b: 255, alpha: 1 },
          })
          .jpeg({ quality: opts.quality })
          .toBuffer();

        // Convert to base64
        const base64Image = `data:image/jpeg;base64,${processedImageBuffer.toString('base64')}`;

        return {
          success: true,
          thumbnail: base64Image,
        };
      } finally {
        // Clean up generated image file
        await fs.promises.unlink(imagePath).catch(() => {});
      }
    } finally {
      // Clean up temporary PDF file
      await fs.promises.unlink(tempPdfPath).catch(() => {});
    }
  } catch (error) {
    return {
      success: false,
      error: `Thumbnail generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Generate multiple thumbnails with different sizes
 */
export async function generateMultipleThumbnails(
  buffer: Buffer,
  sizes: Array<{ name: string; width: number; height: number }>,
): Promise<Record<string, ThumbnailResult>> {
  const results: Record<string, ThumbnailResult> = {};

  await Promise.all(
    sizes.map(async size => {
      results[size.name] = await generatePDFThumbnail(buffer, {
        width: size.width,
        height: size.height,
        quality: 80,
      });
    }),
  );

  return results;
}

/**
 * Validate image dimensions and file size for thumbnails
 */
export function validateThumbnailConstraints(
  width: number,
  height: number,
  maxFileSize: number = 1024 * 1024, // 1MB default
): { valid: boolean; error?: string } {
  if (width < 50 || width > 1000) {
    return { valid: false, error: 'Thumbnail width must be between 50 and 1000 pixels' };
  }

  if (height < 50 || height > 1000) {
    return { valid: false, error: 'Thumbnail height must be between 50 and 1000 pixels' };
  }

  // Estimate size (rough calculation)
  const estimatedSize = width * height * 3; // RGB bytes
  if (estimatedSize > maxFileSize) {
    return { valid: false, error: 'Estimated thumbnail size exceeds maximum allowed size' };
  }

  return { valid: true };
}
