import { z } from 'zod';

import type { ProcessingResult } from './invoice.js';

// File processing status
export type FileStatus = 'queued' | 'processing' | 'completed' | 'failed' | 'partial';

// Queued file representation
export interface QueuedFile {
  id: string;
  name: string;
  size: number;
  status: FileStatus;
  thumbnail?: string; // Base64 encoded image
  uploadTime: number;
  processingStartTime?: number | undefined;
  processingEndTime?: number | undefined;
  attempts?: number; // number of processing attempts
  result?: ProcessingResult;
  error?: string | undefined;
  serverFileId?: string; // File ID confirmed by server after upload
}

// File upload result from API
export interface FileUploadResult {
  id: string;
  thumbnail: string;
}

// Queue summary statistics
export interface QueueStats {
  total: number;
  queued: number;
  processing: number;
  completed: number;
  failed: number;
  partial: number;
}

// Zod schemas for validation
export const FileStatusSchema = z.enum(['queued', 'processing', 'completed', 'failed', 'partial']);

export const QueuedFileSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  size: z.number().positive(),
  status: FileStatusSchema,
  thumbnail: z.string().optional(),
  uploadTime: z.number().positive(),
  processingStartTime: z.number().positive().optional(),
  processingEndTime: z.number().positive().optional(),
  attempts: z.number().int().min(0).optional(),
  result: z.any().optional(), // ProcessingResultSchema would create circular dependency
  error: z.string().optional(),
  serverFileId: z.string().optional(),
});

export const FileUploadResultSchema = z.object({
  id: z.string().min(1),
  thumbnail: z.string().min(1),
});

export const QueueStatsSchema = z.object({
  total: z.number().int().min(0),
  queued: z.number().int().min(0),
  processing: z.number().int().min(0),
  completed: z.number().int().min(0),
  failed: z.number().int().min(0),
  partial: z.number().int().min(0),
});

// Helper functions
export function calculateQueueStats(files: QueuedFile[]): QueueStats {
  return files.reduce(
    (stats, file) => {
      stats.total++;
      stats[file.status]++;
      return stats;
    },
    {
      total: 0,
      queued: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      partial: 0,
    },
  );
}

export function getStatusIcon(status: FileStatus): string {
  switch (status) {
    case 'queued': {
      return '🕒';
    }
    case 'processing': {
      return '🔄';
    }
    case 'completed': {
      return '✅';
    }
    case 'failed': {
      return '❌';
    }
    case 'partial': {
      return '⚠️';
    }
    default: {
      return '📄';
    }
  }
}

export function getStatusColor(status: FileStatus): string {
  switch (status) {
    case 'queued': {
      return 'text-gray-500';
    }
    case 'processing': {
      return 'text-blue-500';
    }
    case 'completed': {
      return 'text-green-500';
    }
    case 'failed': {
      return 'text-red-500';
    }
    case 'partial': {
      return 'text-yellow-500';
    }
    default: {
      return 'text-gray-400';
    }
  }
}
