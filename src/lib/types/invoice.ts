import { z } from 'zod';

// Core invoice data structure
export interface InvoiceData {
  supplier: string;
  invoiceId: string;
  invoiceDate: string;
  dueDate: string;
  total: string;
  vatTotal: string;
}

// Processing metadata
export interface ProcessingMetadata {
  method: 'text' | 'ocr';
  confidenceScore: number;
  processingTime: number;
  attemptCount: number;
  errors?: string[];
}

// Complete processing result
export interface ProcessingResult {
  success: boolean;
  data?: InvoiceData;
  metadata: ProcessingMetadata;
  error?: string;
}

// Zod schemas for validation
export const InvoiceDataSchema = z.object({
  supplier: z.string().min(1, 'Supplier name is required'),
  invoiceId: z.string().min(1, 'Invoice ID is required'),
  invoiceDate: z.string().min(1, 'Invoice date is required'),
  dueDate: z.string().min(1, 'Due date is required'),
  total: z.string().min(1, 'Total amount is required'),
  vatTotal: z.string().min(1, 'VAT total is required'),
});

export const ProcessingMetadataSchema = z.object({
  method: z.enum(['text', 'ocr']),
  confidenceScore: z.number().min(0).max(1),
  processingTime: z.number().positive(),
  attemptCount: z.number().int().positive(),
  errors: z.array(z.string()).optional(),
});

export const ProcessingResultSchema = z.object({
  success: z.boolean(),
  data: InvoiceDataSchema.optional(),
  metadata: ProcessingMetadataSchema,
  error: z.string().optional(),
});

// Type guards
export function isValidInvoiceData(data: unknown): data is InvoiceData {
  return InvoiceDataSchema.safeParse(data).success;
}

export function isValidProcessingResult(result: unknown): result is ProcessingResult {
  return ProcessingResultSchema.safeParse(result).success;
}
