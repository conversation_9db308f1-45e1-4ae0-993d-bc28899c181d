import { z } from 'zod';

// API request/response types
export interface ProcessInvoiceRequest {
  fileId: string;
}

export interface ProcessInvoiceResponse {
  success: boolean;
  data?: {
    supplier: string;
    invoiceId: string;
    invoiceDate: string;
    dueDate: string;
    total: string;
    vatTotal: string;
  };
  metadata: {
    method: 'text' | 'ocr';
    confidenceScore: number;
    processingTime: number;
    attemptCount: number;
    errors?: string[];
  };
  error?: string;
}

export interface GenerateThumbnailResponse {
  success: boolean;
  thumbnail?: string; // Base64 encoded image
  error?: string;
}

// Error log entry
export interface ErrorLogEntry {
  timestamp: number;
  fileId: string;
  fileName: string;
  error: string;
  stage:
    | 'upload'
    | 'thumbnail'
    | 'text-extraction'
    | 'ocr'
    | 'ai-processing'
    | 'processing'
    | 'network';
  attemptNumber: number;
  metadata?: Record<string, unknown>;
}

// Processing configuration
export interface ProcessingConfig {
  maxFileSize: number;
  allowedTypes: string[];
  maxRetries: number;
  timeoutMs: number;
  openrouterConfig: {
    model: string;
    maxTokens: number;
    temperature: number;
  };
}

// Zod schemas
export const ProcessInvoiceRequestSchema = z.object({
  fileId: z.string().min(1),
});

export const ProcessInvoiceResponseSchema = z.object({
  success: z.boolean(),
  data: z
    .object({
      supplier: z.string().min(1),
      invoiceId: z.string().min(1),
      invoiceDate: z.string().min(1),
      dueDate: z.string().min(1),
      total: z.string().min(1),
      vatTotal: z.string().min(1),
    })
    .optional(),
  metadata: z.object({
    method: z.enum(['text', 'ocr']),
    confidenceScore: z.number().min(0).max(1),
    processingTime: z.number().positive(),
    attemptCount: z.number().int().positive(),
    errors: z.array(z.string()).optional(),
  }),
  error: z.string().optional(),
});

export const GenerateThumbnailResponseSchema = z.object({
  success: z.boolean(),
  thumbnail: z.string().optional(),
  error: z.string().optional(),
});

export const ErrorLogEntrySchema = z.object({
  timestamp: z.number().positive(),
  fileId: z.string().min(1),
  fileName: z.string().min(1),
  error: z.string().min(1),
  stage: z.enum([
    'upload',
    'thumbnail',
    'text-extraction',
    'ocr',
    'ai-processing',
    'processing',
    'network',
  ]),
  attemptNumber: z.number().int().positive(),
  metadata: z.record(z.unknown()).optional(),
});

export const ProcessingConfigSchema = z.object({
  maxFileSize: z.number().positive(),
  allowedTypes: z.array(z.string()),
  maxRetries: z.number().int().positive(),
  timeoutMs: z.number().int().positive(),
  openrouterConfig: z.object({
    model: z.string().min(1),
    maxTokens: z.number().int().positive(),
    temperature: z.number().min(0).max(2),
  }),
});

// Default configuration
export const DEFAULT_PROCESSING_CONFIG: ProcessingConfig = {
  maxFileSize: 10 * 1_024 * 1_024, // 10MB
  allowedTypes: ['application/pdf'],
  maxRetries: 1,
  timeoutMs: 120_000, // 2 minutes
  openrouterConfig: {
    model: 'google/gemini-flash-1.5',
    maxTokens: 2_000,
    temperature: 0.1,
  },
};
