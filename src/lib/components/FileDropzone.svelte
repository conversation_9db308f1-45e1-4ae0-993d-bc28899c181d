<script lang="ts">
  import { validateFile, generateFileId } from '$lib/utils/validators.js';
  import { fileQueueStore } from '$lib/stores/fileQueue.js';
  import type { QueuedFile } from '$lib/types/index.js';

  interface Props {
    disabled?: boolean;
    maxFiles?: number;
  }

  let { disabled = false, maxFiles = 10 }: Props = $props();

  let isDragActive = $state(false);
  let isUploading = $state(false);
  let dragCounter = $state(0);

  // Refs
  let fileInput: HTMLInputElement;
  let dropzone: HTMLDivElement;

  async function handleFiles(files: FileList) {
    if (disabled || isUploading) return;

    const fileArray = Array.from(files);
    const pdfFiles = fileArray.filter(file => file.type === 'application/pdf');

    if (pdfFiles.length === 0) {
      alert('Please select PDF files only.');
      return;
    }

    const currentFiles = fileQueueStore.getCurrentFiles();
    if (currentFiles.length + pdfFiles.length > maxFiles) {
      alert(
        `Maximum ${maxFiles} files allowed. You can upload ${maxFiles - currentFiles.length} more files.`,
      );
      return;
    }

    isUploading = true;

    for (const file of pdfFiles) {
      // Validate file
      const validation = validateFile(file);
      if (!validation.valid) {
        console.error(`File validation failed for ${file.name}:`, validation.error);
        continue;
      }

      const fileId = generateFileId();

      // Create queued file entry
      const queuedFile: QueuedFile = {
        id: fileId,
        name: file.name,
        size: file.size,
        status: 'queued',
        uploadTime: Date.now(),
      };

      // Add to queue immediately
      fileQueueStore.addFile(queuedFile);

      // Upload file and generate thumbnail
      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (result.success) {
          // Update with thumbnail and confirm the server fileId
          fileQueueStore.updateFile(fileId, {
            thumbnail: result.thumbnail,
            // Store the confirmed fileId from server (should match our generated one)
            serverFileId: result.fileId,
          });

          if (validation.warnings) {
            console.warn(`Warnings for ${file.name}:`, validation.warnings);
          }
        } else {
          // Mark as failed
          fileQueueStore.updateFile(fileId, {
            status: 'failed',
            error: result.error || 'Upload failed',
          });
        }
      } catch (error) {
        console.error('Upload error:', error);
        fileQueueStore.updateFile(fileId, {
          status: 'failed',
          error: 'Network error during upload',
        });
      }
    }

    isUploading = false;

    // Clear input
    if (fileInput) {
      fileInput.value = '';
    }
  }

  function handleDragEnter(e: DragEvent) {
    e.preventDefault();
    dragCounter++;
    isDragActive = true;
  }

  function handleDragLeave(e: DragEvent) {
    e.preventDefault();
    dragCounter--;
    if (dragCounter === 0) {
      isDragActive = false;
    }
  }

  function handleDragOver(e: DragEvent) {
    e.preventDefault();
  }

  function handleDrop(e: DragEvent) {
    e.preventDefault();
    isDragActive = false;
    dragCounter = 0;

    if (e.dataTransfer?.files) {
      handleFiles(e.dataTransfer.files);
    }
  }

  function handleInputChange(e: Event) {
    const target = e.target as HTMLInputElement;
    if (target.files) {
      handleFiles(target.files);
    }
  }

  function openFileDialog() {
    if (!disabled && !isUploading) {
      fileInput?.click();
    }
  }
</script>

<div
  bind:this={dropzone}
  class="dropzone"
  class:drag-active={isDragActive}
  class:disabled={disabled || isUploading}
  ondragenter={handleDragEnter}
  ondragleave={handleDragLeave}
  ondragover={handleDragOver}
  ondrop={handleDrop}
  onclick={openFileDialog}
  role="button"
  tabindex="0"
  onkeydown={e => e.key === 'Enter' && openFileDialog()}
>
  <input
    bind:this={fileInput}
    type="file"
    accept=".pdf,application/pdf"
    multiple
    onchange={handleInputChange}
    style="display: none"
    aria-hidden="true"
  />

  <div class="dropzone-content">
    {#if isUploading}
      <div class="loading-spinner"></div>
      <p class="dropzone-text">Uploading files...</p>
    {:else if disabled}
      <div class="icon">📄</div>
      <p class="dropzone-text">File upload disabled</p>
    {:else}
      <div class="icon">📁</div>
      <p class="dropzone-text">
        {#if isDragActive}
          Drop PDF files here
        {:else}
          Drag and drop PDF files here, or click to select
        {/if}
      </p>
      <p class="dropzone-subtext">
        Maximum {maxFiles} files, 10MB each
      </p>
    {/if}
  </div>
</div>

<style>
  .dropzone {
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background: #f8fafc;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dropzone:hover:not(.disabled) {
    border-color: #4299e1;
    background: #ebf8ff;
  }

  .dropzone:focus:not(.disabled) {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .dropzone.drag-active {
    border-color: #48bb78;
    background: #f0fff4;
    transform: scale(1.02);
  }

  .dropzone.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f7fafc;
  }

  .dropzone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .icon {
    font-size: 3rem;
    opacity: 0.7;
  }

  .dropzone-text {
    font-size: 1.125rem;
    font-weight: 500;
    color: #2d3748;
    margin: 0;
  }

  .dropzone-subtext {
    font-size: 0.875rem;
    color: #718096;
    margin: 0;
  }

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @media (max-width: 640px) {
    .dropzone {
      padding: 1.5rem;
      min-height: 150px;
    }

    .icon {
      font-size: 2rem;
    }

    .dropzone-text {
      font-size: 1rem;
    }

    .dropzone-subtext {
      font-size: 0.75rem;
    }
  }
</style>
