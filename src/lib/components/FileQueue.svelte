<script lang="ts">
  import { fileQueueStore } from '$lib/stores/fileQueue.js';
  import { processingStore } from '$lib/stores/processing.js';
  import { selectedFileStore } from '$lib/stores/selectedFile.js';
  import { getStatusIcon, getStatusColor } from '$lib/types/queue.js';
  import { formatBytes } from '$lib/utils/validators.js';

  // Reactive store data - Svelte 5 runes ($ to unwrap readables in template)
  const filesStore = fileQueueStore.files;
  const statsStore = fileQueueStore.stats;
  const isProcessingStore = fileQueueStore.isProcessing;
  const selectedFileStoreReadable = selectedFileStore.selectedFile;

  function selectFile(file: import('$lib/types').QueuedFile) {
    selectedFileStore.selectFile(file);
  }

  function removeFile(fileId: string) {
    // Capture current selection synchronously
    let currentSelection: import('$lib/types').QueuedFile | undefined;
    selectedFileStoreReadable.subscribe(v => (currentSelection = v))();
    fileQueueStore.removeFile(fileId);
    if (currentSelection?.id === fileId) selectedFileStore.clearSelection();
  }

  function retryFile(fileId: string) {
    fileQueueStore.retryFile(fileId);
  }

  function clearProcessed() {
    let currentSelection: import('$lib/types').QueuedFile | undefined;
    selectedFileStoreReadable.subscribe(v => (currentSelection = v))();
    if (currentSelection && ['completed', 'failed'].includes(currentSelection.status)) {
      selectedFileStore.clearSelection();
    }
    fileQueueStore.clearProcessed();
  }

  function clearAll() {
    selectedFileStore.clearSelection();
    fileQueueStore.clearAll();
  }

  function startProcessing() {
    // Use the store's built-in queue processing logic
    fileQueueStore.processQueue();
  }

  function formatTimestamp(timestamp: number): string {
    return new Date(timestamp).toLocaleTimeString();
  }

  function getProcessingDuration(file: import('$lib/types').QueuedFile): string {
    if (!file.processingStartTime) return '';
    const endTime = file.processingEndTime || Date.now();
    const duration = endTime - file.processingStartTime;
    return `${Math.round(duration / 1_000)}s`;
  }
</script>

<div class="file-queue">
  <div class="queue-header">
    <h3>File Queue</h3>
    <div class="queue-stats">
      <span class="stat">
        <span class="stat-value">{$statsStore.total}</span>
        <span class="stat-label">Total</span>
      </span>
      <span class="stat">
        <span class="stat-value">{$statsStore.completed}</span>
        <span class="stat-label">Done</span>
      </span>
      <span class="stat">
        <span class="stat-value">{$statsStore.failed}</span>
        <span class="stat-label">Failed</span>
      </span>
    </div>
  </div>

  {#if $filesStore.length === 0}
    <div class="empty-state">
      <p>No files in queue</p>
      <p class="empty-subtext">Upload PDF files to get started</p>
    </div>
  {:else}
    <div class="queue-controls">
      <button
        class="btn btn-primary"
        onclick={startProcessing}
        disabled={$isProcessingStore || $statsStore.queued === 0}
      >
        {$isProcessingStore ? 'Processing...' : 'Start Processing'}
      </button>

      <div class="control-group">
        <button
          class="btn btn-secondary btn-sm"
          onclick={clearProcessed}
          disabled={$statsStore.completed === 0 && $statsStore.failed === 0}
        >
          Clear Processed
        </button>
        <button
          class="btn btn-danger btn-sm"
          onclick={clearAll}
          disabled={$filesStore.length === 0}
        >
          Clear All
        </button>
      </div>
    </div>

    <div class="file-list">
      {#each $filesStore as file (file.id)}
        <div
          class="file-item"
          class:selected={$selectedFileStoreReadable?.id === file.id}
          class:processing={file.status === 'processing'}
          onclick={() => selectFile(file)}
          role="button"
          tabindex="0"
          onkeydown={e => e.key === 'Enter' && selectFile(file)}
        >
          <div class="file-thumbnail">
            {#if file.thumbnail}
              <img src={file.thumbnail} alt="PDF thumbnail" />
            {:else}
              <div class="placeholder-thumbnail">📄</div>
            {/if}
          </div>

          <div class="file-info">
            <div class="file-name" title={file.name}>
              {file.name}
            </div>
            <div class="file-meta">
              <span class="file-size">{formatBytes(file.size)}</span>
              <span class="file-time">{formatTimestamp(file.uploadTime)}</span>
              {#if file.processingStartTime}
                <span class="processing-time">{getProcessingDuration(file)}</span>
              {/if}
            </div>
            {#if file.error}
              <div class="error-message" title={file.error}>
                {file.error}
              </div>
            {/if}
          </div>

          <div class="file-status">
            <span class="status-icon {getStatusColor(file.status)}" title={file.status}>
              {getStatusIcon(file.status)}
            </span>

            <div class="file-actions">
              {#if file.status === 'failed'}
                <button
                  class="action-btn retry-btn"
                  onclick={e => {
                    e.stopPropagation();
                    retryFile(file.id);
                  }}
                  title="Retry processing"
                >
                  🔄
                </button>
              {/if}
              <button
                class="action-btn remove-btn"
                onclick={e => {
                  e.stopPropagation();
                  removeFile(file.id);
                }}
                title="Remove file"
              >
                🗑️
              </button>
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .file-queue {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-right: 1px solid #e2e8f0;
  }

  .queue-header {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
  }

  .queue-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
  }

  .queue-stats {
    display: flex;
    gap: 1rem;
  }

  .stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }

  .stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
  }

  .stat-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    font-weight: 500;
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: #718096;
  }

  .empty-subtext {
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }

  .queue-controls {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .control-group {
    display: flex;
    gap: 0.5rem;
  }

  .file-list {
    flex: 1;
    overflow-y: auto;
  }

  .file-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.15s ease;
  }

  .file-item:hover {
    background: #f8fafc;
  }

  .file-item.selected {
    background: #ebf8ff;
    border-color: #4299e1;
  }

  .file-item.processing {
    background: #fffbeb;
    border-left: 3px solid #f6ad55;
  }

  .file-thumbnail {
    width: 48px;
    height: 64px;
    margin-right: 0.75rem;
    flex-shrink: 0;
  }

  .file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
  }

  .placeholder-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 1.5rem;
    color: #a0aec0;
  }

  .file-info {
    flex: 1;
    min-width: 0;
  }

  .file-name {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.25rem;
  }

  .file-meta {
    display: flex;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #718096;
  }

  .error-message {
    font-size: 0.75rem;
    color: #e53e3e;
    margin-top: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .file-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    margin-left: 0.5rem;
  }

  .status-icon {
    font-size: 1.25rem;
  }

  .file-actions {
    display: flex;
    gap: 0.25rem;
  }

  .action-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: background-color 0.15s ease;
  }

  .action-btn:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  .retry-btn:hover {
    background: rgba(72, 187, 120, 0.1);
  }

  .remove-btn:hover {
    background: rgba(229, 62, 62, 0.1);
  }

  /* Button styles */
  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background: #4299e1;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #3182ce;
  }

  .btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
  }

  .btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
  }

  .btn-danger {
    background: #fed7d7;
    color: #c53030;
  }

  .btn-danger:hover:not(:disabled) {
    background: #feb2b2;
  }

  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
</style>
