<script lang="ts">
  import { fileQueueStore } from '$lib/stores/fileQueue.js';
  import { selectedFileStore } from '$lib/stores/selectedFile.js';
  import type { QueuedFile, InvoiceData, ProcessingMetadata } from '$lib/types/index.js';

  // Get reactive selected file - use the store directly
  const selectedFileStore_selectedFile = selectedFileStore.selectedFile;

  // Derived data - use the store values directly in template with $
  // These will be accessed as $selectedFileStore_selectedFile in the template

  let showMetadata = $state(false);

  function copyToClipboard(text: string) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        // Could add a toast notification here
        console.log('Copied to clipboard:', text);
      })
      .catch(error => {
        console.error('Failed to copy:', error);
      });
  }

  function copyAllData() {
    const currentFile = $selectedFileStore_selectedFile;
    if (!currentFile?.result?.data) return;

    const data = {
      supplier: currentFile.result.data.supplier,
      invoiceId: currentFile.result.data.invoiceId,
      invoiceDate: currentFile.result.data.invoiceDate,
      dueDate: currentFile.result.data.dueDate,
      total: currentFile.result.data.total,
      vatTotal: currentFile.result.data.vatTotal,
    };

    const jsonString = JSON.stringify(data, null, 2);
    copyToClipboard(jsonString);
  }

  function retryProcessing() {
    const currentFile = $selectedFileStore_selectedFile;
    if (currentFile) {
      fileQueueStore.retryFile(currentFile.id);
    }
  }

  function formatConfidenceScore(score: number): string {
    return `${Math.round(score * 100)}%`;
  }

  function formatProcessingTime(timeMs: number): string {
    if (timeMs < 1_000) return `${timeMs}ms`;
    return `${(timeMs / 1_000).toFixed(1)}s`;
  }

  function getConfidenceColor(score: number): string {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  }

  function getMethodBadgeClass(method: 'text' | 'ocr'): string {
    return method === 'text' ? 'badge-success' : 'badge-warning';
  }
</script>

<div class="results-panel">
  {#if !$selectedFileStore_selectedFile}
    <div class="empty-state">
      <div class="icon">📄</div>
      <h3>No File Selected</h3>
      <p>Select a file from the queue to view its processing results</p>
    </div>
  {:else if $selectedFileStore_selectedFile.status === 'queued'}
    <div class="status-state">
      <div class="icon">🕒</div>
      <h3>Waiting to Process</h3>
      <p class="filename">{$selectedFileStore_selectedFile.name}</p>
      <p class="status-text">This file is queued for processing</p>
    </div>
  {:else if $selectedFileStore_selectedFile.status === 'processing'}
    <div class="status-state">
      <div class="loading-spinner"></div>
      <h3>Processing...</h3>
      <p class="filename">{$selectedFileStore_selectedFile.name}</p>
      <p class="status-text">Extracting invoice data</p>
    </div>
  {:else if $selectedFileStore_selectedFile.status === 'failed'}
    <div class="error-state">
      <div class="icon">❌</div>
      <h3>Processing Failed</h3>
      <p class="filename">{$selectedFileStore_selectedFile.name}</p>
      <div class="error-message">
        {$selectedFileStore_selectedFile.error || 'Unknown error occurred'}
      </div>
      <button class="btn btn-primary" onclick={retryProcessing}> Retry Processing </button>
    </div>
  {:else if $selectedFileStore_selectedFile.status === 'completed' && $selectedFileStore_selectedFile.result?.data}
    <div class="success-state">
      <div class="results-header">
        <div class="header-content">
          <h3>Invoice Data Extracted</h3>
          <p class="filename">{$selectedFileStore_selectedFile.name}</p>
          {#if $selectedFileStore_selectedFile.status === 'partial'}
            <div class="warning-badge">⚠️ Partial extraction - some data may be missing</div>
          {/if}
        </div>
        <button class="btn btn-secondary btn-sm" onclick={copyAllData}> 📋 Copy All </button>
      </div>

      <div class="invoice-data">
        <div class="data-row">
          <label>Supplier</label>
          <div class="data-value">
            <span class="value">{$selectedFileStore_selectedFile.result?.data?.supplier}</span>
            <button
              class="copy-btn"
              onclick={() =>
                copyToClipboard($selectedFileStore_selectedFile.result?.data?.supplier || '')}
              >📋</button
            >
          </div>
        </div>

        <div class="data-row">
          <label>Invoice ID</label>
          <div class="data-value">
            <span class="value">{$selectedFileStore_selectedFile.result?.data?.invoiceId}</span>
            <button
              class="copy-btn"
              onclick={() =>
                copyToClipboard($selectedFileStore_selectedFile.result?.data?.invoiceId || '')}
              >📋</button
            >
          </div>
        </div>

        <div class="data-row">
          <label>Invoice Date</label>
          <div class="data-value">
            <span class="value">{$selectedFileStore_selectedFile.result?.data?.invoiceDate}</span>
            <button
              class="copy-btn"
              onclick={() =>
                copyToClipboard($selectedFileStore_selectedFile.result?.data?.invoiceDate || '')}
              >📋</button
            >
          </div>
        </div>

        <div class="data-row">
          <label>Due Date</label>
          <div class="data-value">
            <span class="value">{$selectedFileStore_selectedFile.result?.data?.dueDate}</span>
            <button
              class="copy-btn"
              onclick={() =>
                copyToClipboard($selectedFileStore_selectedFile.result?.data?.dueDate || '')}
              >📋</button
            >
          </div>
        </div>

        <div class="data-row highlight">
          <label>Total Amount</label>
          <div class="data-value">
            <span class="value total-amount"
              >{$selectedFileStore_selectedFile.result?.data?.total}</span
            >
            <button
              class="copy-btn"
              onclick={() =>
                copyToClipboard($selectedFileStore_selectedFile.result?.data?.total || '')}
              >📋</button
            >
          </div>
        </div>

        <div class="data-row">
          <label>VAT Total</label>
          <div class="data-value">
            <span class="value">{$selectedFileStore_selectedFile.result?.data?.vatTotal}</span>
            <button
              class="copy-btn"
              onclick={() =>
                copyToClipboard($selectedFileStore_selectedFile.result?.data?.vatTotal || '')}
              >📋</button
            >
          </div>
        </div>
      </div>

      {#if $selectedFileStore_selectedFile.result?.metadata}
        <div class="metadata-section">
          <button class="metadata-toggle" onclick={() => (showMetadata = !showMetadata)}>
            <span>Processing Details</span>
            <span class="toggle-icon" class:rotated={showMetadata}>▼</span>
          </button>

          {#if showMetadata}
            <div class="metadata-content">
              <div class="metadata-grid">
                <div class="metadata-item">
                  <label>Extraction Method</label>
                  <span
                    class="badge {getMethodBadgeClass(
                      $selectedFileStore_selectedFile.result?.metadata?.method || 'text',
                    )}"
                  >
                    {$selectedFileStore_selectedFile.result?.metadata?.method?.toUpperCase() ||
                      'TEXT'}
                  </span>
                </div>

                <div class="metadata-item">
                  <label>Confidence Score</label>
                  <span
                    class="confidence {getConfidenceColor(
                      $selectedFileStore_selectedFile.result?.metadata?.confidenceScore || 0,
                    )}"
                  >
                    {formatConfidenceScore(
                      $selectedFileStore_selectedFile.result?.metadata?.confidenceScore || 0,
                    )}
                  </span>
                </div>

                <div class="metadata-item">
                  <label>Processing Time</label>
                  <span
                    >{formatProcessingTime(
                      $selectedFileStore_selectedFile.result?.metadata?.processingTime || 0,
                    )}</span
                  >
                </div>

                <div class="metadata-item">
                  <label>Attempts</label>
                  <span>{$selectedFileStore_selectedFile.result?.metadata?.attemptCount || 0}</span>
                </div>
              </div>

              {#if $selectedFileStore_selectedFile.result?.metadata?.errors && $selectedFileStore_selectedFile.result.metadata.errors.length > 0}
                <div class="errors-section">
                  <h4>Processing Notes</h4>
                  <ul>
                    {#each $selectedFileStore_selectedFile.result.metadata.errors as error}
                      <li>{error}</li>
                    {/each}
                  </ul>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .results-panel {
    height: 100%;
    overflow-y: auto;
    background: white;
  }

  .empty-state,
  .status-state,
  .error-state {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
  }

  .loading-spinner {
    width: 4rem;
    height: 4rem;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .empty-state h3,
  .status-state h3,
  .error-state h3 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .filename {
    font-weight: 500;
    color: #4a5568;
    margin: 0 0 1rem 0;
    word-break: break-word;
  }

  .status-text {
    color: #718096;
    margin: 0;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    font-family: monospace;
    font-size: 0.875rem;
    word-break: break-word;
  }

  .warning-badge {
    background: #fef5e7;
    color: #d69e2e;
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }

  .success-state {
    padding: 1.5rem;
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .header-content h3 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  .invoice-data {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .data-row:last-child {
    border-bottom: none;
  }

  .data-row.highlight {
    background: rgba(72, 187, 120, 0.1);
    margin: 0 -1rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    border: none;
  }

  .data-row label {
    font-weight: 500;
    color: #4a5568;
    font-size: 0.875rem;
  }

  .data-value {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .value {
    font-weight: 500;
    color: #2d3748;
  }

  .total-amount {
    font-size: 1.125rem;
    font-weight: 700;
    color: #38a169;
  }

  .copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    opacity: 0.7;
    transition: opacity 0.15s ease;
    padding: 0.25rem;
    border-radius: 4px;
  }

  .copy-btn:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
  }

  .metadata-section {
    border-top: 2px solid #e2e8f0;
    padding-top: 1.5rem;
  }

  .metadata-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    transition: background-color 0.15s ease;
  }

  .metadata-toggle:hover {
    background: #f8fafc;
  }

  .toggle-icon {
    transition: transform 0.15s ease;
  }

  .toggle-icon.rotated {
    transform: rotate(180deg);
  }

  .metadata-content {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 6px;
    margin-top: 0.5rem;
  }

  .metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .metadata-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .metadata-item label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    font-weight: 500;
  }

  .badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    width: fit-content;
  }

  .badge-success {
    background: #c6f6d5;
    color: #22543d;
  }

  .badge-warning {
    background: #fef5e7;
    color: #744210;
  }

  .confidence {
    font-weight: 600;
  }

  .errors-section {
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
  }

  .errors-section h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    margin: 0 0 0.5rem 0;
  }

  .errors-section ul {
    margin: 0;
    padding-left: 1rem;
    color: #718096;
    font-size: 0.875rem;
  }

  /* Button styles */
  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background: #4299e1;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #3182ce;
  }

  .btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
  }

  .btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
  }

  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  @media (max-width: 640px) {
    .results-header {
      flex-direction: column;
      gap: 1rem;
    }

    .metadata-grid {
      grid-template-columns: 1fr;
    }

    .data-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .data-value {
      align-self: flex-end;
    }
  }
</style>
