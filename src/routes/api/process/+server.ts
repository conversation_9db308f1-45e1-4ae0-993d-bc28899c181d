import { existsSync } from 'node:fs';
import { readFile } from 'node:fs/promises';
import path from 'node:path';

import { json } from '@sveltejs/kit';
import type { z } from 'zod';

import { DEFAULT_PROCESSING_CONFIG, ProcessInvoiceRequestSchema } from '$lib/types/processing.js';
import { extractInvoiceData } from '$lib/utils/gemini-client.js';
import { logError } from '$lib/utils/logger.js';
import { processPDF } from '$lib/utils/pdf-processor.js';

import type { RequestHandler } from './$types.js';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const fileId = url.searchParams.get('id');
    if (!fileId) {
      return json(
        {
          success: false,
          error: 'File ID is required',
        },
        { status: 400 },
      );
    }

    // Check if file exists in temporary storage
    const tempFilePath = path.join(process.cwd(), 'temp', 'uploads', `${fileId}.pdf`);
    const fileExists = existsSync(tempFilePath);

    return json({
      success: true,
      fileId,
      exists: fileExists,
      path: fileExists ? tempFilePath : null,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return json(
      {
        success: false,
        error: `Failed to check file status: ${errorMessage}`,
      },
      { status: 500 },
    );
  }
};

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = (await request.json()) as z.infer<typeof ProcessInvoiceRequestSchema>;
    const requestValidation = ProcessInvoiceRequestSchema.safeParse(body);
    if (!requestValidation.success) {
      return json(
        {
          success: false,
          metadata: {
            method: 'text',
            confidenceScore: 0,
            processingTime: 0,
            attemptCount: 0,
          },
          error: 'Invalid request format',
        },
        { status: 400 },
      );
    }
    const { fileId } = requestValidation.data;
    // Get file from temporary storage
    const tempFilePath = path.join(process.cwd(), 'temp', 'uploads', `${fileId}.pdf`);
    if (!existsSync(tempFilePath)) {
      return json(
        {
          success: false,
          metadata: {
            method: 'text',
            confidenceScore: 0,
            processingTime: 0,
            attemptCount: 0,
          },
          error: 'File not found for processing - may have been deleted or never uploaded',
        },
        { status: 404 },
      );
    }
    const startTime = Date.now();
    // Get filename from fileId (you might want to store original filename separately)
    const fileName = `${fileId}.pdf`;
    // Retry loop (attempt once, then retry once if fails)
    const processFile = async (fileId: string, fileName: string, tempFilePath: string) => {
      const buffer = await readFile(tempFilePath);
      const textResult = await processPDF(buffer);
      if (!textResult.text || textResult.text.trim().length < 20) {
        throw new Error('Insufficient text extracted from PDF');
      }
      const aiResult = await extractInvoiceData(textResult.text, {
        model: DEFAULT_PROCESSING_CONFIG.openrouterConfig.model,
        maxTokens: DEFAULT_PROCESSING_CONFIG.openrouterConfig.maxTokens,
        temperature: DEFAULT_PROCESSING_CONFIG.openrouterConfig.temperature,
        maxRetries: 0,
      });
      if (!aiResult.success || !aiResult.data) {
        throw new Error(aiResult.error ?? 'AI failed to extract invoice data');
      }
      return { textResult, aiResult };
    };

    let attempt = 0;
    let lastError: string | undefined;

    // Process with single retry
    for (let i = 0; i <= DEFAULT_PROCESSING_CONFIG.maxRetries; i++) {
      try {
        const { aiResult, textResult } = await processFile(fileId, fileName, tempFilePath);
        const totalProcessingTime = Date.now() - startTime;
        const combinedConfidenceScore =
          (textResult.metadata.confidenceScore ?? 0.5) * 0.3 + aiResult.confidenceScore * 0.7;
        return json({
          success: true,
          data: aiResult.data,
          metadata: {
            method: textResult.method,
            confidenceScore: combinedConfidenceScore,
            processingTime: totalProcessingTime,
            attemptCount: attempt + 1,
          },
        });
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Unknown error';
        attempt++;

        // Don't delay on the last attempt
        if (i < DEFAULT_PROCESSING_CONFIG.maxRetries) {
          const baseDelay = 1_000;
          const delayMs = baseDelay + i * baseDelay; // Linear backoff: 1s, 2s, 3s...
          // eslint-disable-next-line no-await-in-loop
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      }
    }

    const totalProcessingTime = Date.now() - startTime;
    return json(
      {
        success: false,
        metadata: {
          method: 'text',
          confidenceScore: 0,
          processingTime: totalProcessingTime,
          attemptCount: attempt,
          errors: [lastError ?? 'Processing failed'],
        },
        error: `Processing failed after ${DEFAULT_PROCESSING_CONFIG.maxRetries + 1} attempts: ${lastError}`,
      },
      { status: 500 },
    );
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return json(
      {
        success: false,
        metadata: {
          method: 'text',
          confidenceScore: 0,
          processingTime: 0,
          attemptCount: 0,
        },
        error: `Request processing failed: ${errorMessage}`,
      },
      { status: 500 },
    );
  }
};
