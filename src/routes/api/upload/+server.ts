import { existsSync } from 'node:fs';
import { writeFile, mkdir } from 'node:fs/promises';
import path from 'node:path';

import { json } from '@sveltejs/kit';

import { DEFAULT_PROCESSING_CONFIG } from '$lib/types/processing.js';
import { logError } from '$lib/utils/logger.js';
import { validatePDF } from '$lib/utils/pdf-processor.js';
import { generatePDFThumbnail } from '$lib/utils/thumbnail-generator.js';
import { generateFileId, validateFile, validatePDFContent } from '$lib/utils/validators.js';

import type { RequestHandler } from './$types.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    // Defensive: SvelteKit's FormData.get('file') can be null if not present, but type assertion is not always safe.
    if (!(file instanceof File)) {
      return json(
        {
          success: false,
          error: 'No file provided',
        },
        { status: 400 },
      );
    }

    // Generate unique file ID
    const fileId = generateFileId();

    // Validate file constraints
    const fileValidation = validateFile(file, {
      maxSize: DEFAULT_PROCESSING_CONFIG.maxFileSize,
      allowedTypes: DEFAULT_PROCESSING_CONFIG.allowedTypes,
      allowedExtensions: ['.pdf'],
    });

    if (!fileValidation.valid) {
      await logError(fileId, file.name, fileValidation.error ?? 'File validation failed', 'upload');

      return json(
        {
          success: false,
          error: fileValidation.error,
        },
        { status: 400 },
      );
    }

    try {
      // Convert file to buffer for processing
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Validate PDF content
      const contentValidation = validatePDFContent(arrayBuffer);
      if (!contentValidation.valid) {
        await logError(
          fileId,
          file.name,
          contentValidation.error ?? 'PDF content validation failed',
          'upload',
        );

        return json(
          {
            success: false,
            error: contentValidation.error,
          },
          { status: 400 },
        );
      }

      // Additional PDF structure validation using our utility
      const pdfValidation = validatePDF(buffer);
      if (!pdfValidation.valid) {
        await logError(
          fileId,
          file.name,
          pdfValidation.error ?? 'PDF structure validation failed',
          'upload',
        );

        return json(
          {
            success: false,
            error: pdfValidation.error,
          },
          { status: 400 },
        );
      }

      // Generate thumbnail
      const thumbnailResult = await generatePDFThumbnail(buffer, {
        width: 200,
        height: 260,
        quality: 80,
      });

      if (!thumbnailResult.success) {
        // Log thumbnail error but don't fail the upload
        await logError(
          fileId,
          file.name,
          thumbnailResult.error ?? 'Thumbnail generation failed',
          'thumbnail',
        );

        // Return success but without thumbnail
        return json({
          success: true,
          fileId,
          error: 'File uploaded successfully but thumbnail generation failed',
        });
      }

      // Store the file temporarily for processing
      const tempDir = path.join(process.cwd(), 'temp', 'uploads');
      if (!existsSync(tempDir)) {
        await mkdir(tempDir, { recursive: true });
      }

      const tempFilePath = path.join(tempDir, `${fileId}.pdf`);
      await writeFile(tempFilePath, buffer);

      return json({
        success: true,
        fileId,
        thumbnail: thumbnailResult.thumbnail,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error during file processing';
      await logError(fileId, file.name, errorMessage, 'upload');

      return json(
        {
          success: false,
          error: `File processing failed: ${errorMessage}`,
        },
        { status: 500 },
      );
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    return json(
      {
        success: false,
        error: `Upload failed: ${errorMessage}`,
      },
      { status: 500 },
    );
  }
};
