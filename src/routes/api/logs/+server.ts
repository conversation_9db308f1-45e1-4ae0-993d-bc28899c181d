import { json } from '@sveltejs/kit';

import type { ErrorLogEntry } from '$lib/types/index.js';
import { getLogs, getLogsForFile, logError } from '$lib/utils/logger.js';

import type { RequestHandler } from './$types.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const payload = (await request.json()) as {
      fileId: string;
      fileName: string;
      error: string;
      stage: string;
    };
    const { error, fileId, fileName, stage } = payload;
    await logError(
      String(fileId),
      String(fileName),
      String(error),
      stage as
        | 'upload'
        | 'thumbnail'
        | 'text-extraction'
        | 'ocr'
        | 'ai-processing'
        | 'processing'
        | 'network',
    );
    return json({ success: true });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return json(
      {
        success: false,
        error: `Failed to log error: ${errorMessage}`,
      },
      { status: 500 },
    );
  }
};

export const GET: RequestHandler = async ({ url }) => {
  try {
    const fileId = url.searchParams.get('fileId');
    const limit = Number.parseInt(url.searchParams.get('limit') ?? '50', 10);
    let logs: ErrorLogEntry[];
    logs = await (fileId ? getLogsForFile(fileId) : getLogs());
    if (limit > 0) {
      logs = logs.slice(0, limit);
    }
    return json({
      success: true,
      logs,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return json(
      {
        success: false,
        error: `Failed to retrieve logs: ${errorMessage}`,
      },
      { status: 500 },
    );
  }
};
