<script lang="ts">
  import { onMount } from 'svelte';
  import { cubicOut } from 'svelte/easing';
  import { fade, scale, fly } from 'svelte/transition';

  interface TimeLeft {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }

  // Target date - default to 7 days from now
  const targetDate = $state(new Date(Date.now() + 7 * 24 * 60 * 60 * 1_000));
  let timeLeft = $state<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  let prevTimeLeft = $state<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  let isActive = $state(true);
  let isCompleted = $state(false);
  let intervalId: number | undefined;
  let mounted = $state(false);

  const calculateTimeLeft = (): TimeLeft => {
    const difference = targetDate.getTime() - Date.now();

    if (difference > 0) {
      return {
        days: Math.floor(difference / (1_000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1_000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1_000 / 60) % 60),
        seconds: Math.floor((difference / 1_000) % 60),
      };
    }

    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  };

  const updateTimer = () => {
    if (isActive) {
      prevTimeLeft = { ...timeLeft };
      const newTimeLeft = calculateTimeLeft();
      timeLeft = newTimeLeft;

      if (
        newTimeLeft.days === 0 &&
        newTimeLeft.hours === 0 &&
        newTimeLeft.minutes === 0 &&
        newTimeLeft.seconds === 0 &&
        !isCompleted
      ) {
        isCompleted = true;
        console.log('Countdown completed!');
      }
    }
  };

  const toggleTimer = () => {
    isActive = !isActive;
  };

  const resetTimer = () => {
    isCompleted = false;
    timeLeft = calculateTimeLeft();
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  onMount(() => {
    timeLeft = calculateTimeLeft();
    prevTimeLeft = { ...timeLeft };
    mounted = true;

    intervalId = setInterval(updateTimer, 1_000);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  });
</script>

<svelte:head>
  <title>Countdown Timer</title>
  <meta name="description" content="A simple countdown timer" />
</svelte:head>

<div
  class="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50 p-4"
>
  {#if mounted}
    <div class="mx-auto w-full max-w-4xl" in:fade={{ duration: 600, easing: cubicOut }}>
      <div
        class="overflow-hidden rounded-2xl border border-gray-200 bg-white/95 shadow-2xl backdrop-blur-sm"
        in:scale={{ duration: 500, delay: 200, easing: cubicOut, start: 0.9 }}
      >
        <!-- Header -->
        <div
          class="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8 text-center text-white"
          in:fly={{ y: -20, duration: 500, delay: 400, easing: cubicOut }}
        >
          <h1 class="mb-2 text-3xl font-bold md:text-4xl">Product Launch</h1>
          <p class="mb-4 text-lg text-blue-100">Get ready for something amazing!</p>

          <div class="flex items-center justify-center gap-2 text-sm text-blue-200">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              ></path>
            </svg>
            <span>{formatDate(targetDate)}</span>
          </div>
        </div>

        <!-- Content -->
        <div class="space-y-8 p-8">
          {#if isCompleted}
            <div
              class="py-12 text-center"
              in:scale={{ duration: 500, easing: cubicOut, start: 0.8 }}
            >
              <div class="mb-4 text-6xl">🎉</div>
              <h2 class="mb-2 text-2xl font-bold text-gray-800">Time's Up!</h2>
              <p class="text-gray-600">The countdown has reached zero!</p>
            </div>
          {:else}
            <!-- Timer Display -->
            <div class="grid grid-cols-2 gap-4 md:grid-cols-4 md:gap-6">
              {#each [{ value: timeLeft.days, label: 'Days', key: 'days' }, { value: timeLeft.hours, label: 'Hours', key: 'hours' }, { value: timeLeft.minutes, label: 'Minutes', key: 'minutes' }, { value: timeLeft.seconds, label: 'Seconds', key: 'seconds' }] as unit, index}
                <div
                  class="rounded-xl border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-purple-50 p-6 text-center transition-all duration-300 hover:scale-105 hover:border-blue-300"
                  in:fly={{ y: 20, duration: 500, delay: 600 + index * 100, easing: cubicOut }}
                >
                  <div class="relative mb-2 flex h-12 items-center justify-center md:h-14">
                    {#key unit.value}
                      <div
                        class="absolute text-3xl font-bold text-blue-600 md:text-4xl"
                        in:fly={{ y: 10, duration: 200, easing: cubicOut }}
                        out:fly={{ y: -10, duration: 200, easing: cubicOut }}
                      >
                        {unit.value.toString().padStart(2, '0')}
                      </div>
                    {/key}
                  </div>
                  <div class="text-sm font-medium tracking-wider text-gray-600 uppercase">
                    {unit.label}
                  </div>
                </div>
              {/each}
            </div>
          {/if}

          <!-- Controls -->
          <div
            class="flex flex-col items-center justify-center gap-4 pt-6 sm:flex-row"
            in:fly={{ y: 20, duration: 500, delay: 1_000, easing: cubicOut }}
          >
            <div class="flex items-center gap-2">
              <svg
                class="h-4 w-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
              <span
                class="rounded-full px-3 py-1 text-xs font-medium {isActive
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'}"
              >
                {isActive ? 'Running' : 'Paused'}
              </span>
            </div>

            <div class="flex gap-2">
              <button
                onclick={toggleTimer}
                class="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-200 hover:scale-105 hover:bg-gray-50"
              >
                {#if isActive}
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="6" y="4" width="4" height="16"></rect>
                    <rect x="14" y="4" width="4" height="16"></rect>
                  </svg>
                  Pause
                {:else}
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <polygon points="5,3 19,12 5,21"></polygon>
                  </svg>
                  Resume
                {/if}
              </button>

              {#if isCompleted}
                <button
                  onclick={resetTimer}
                  class="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-all duration-200 hover:scale-105 hover:bg-blue-700"
                  in:scale={{ duration: 300, easing: cubicOut, start: 0.8 }}
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <polyline points="1,4 1,10 7,10"></polyline>
                    <path
                      d="M3.51,15a9,9,0,0,0,2.13,3.09,9,9,0,0,0,13.72,0,9,9,0,0,0,0-12.72,9,9,0,0,0-13.72,0,9,9,0,0,0-2.13,3.09"
                    ></path>
                  </svg>
                  Reset
                </button>
              {/if}
            </div>
          </div>

          <!-- Progress Bar -->
          <div
            class="h-2 w-full overflow-hidden rounded-full bg-gray-200"
            in:fade={{ duration: 500, delay: 1_200 }}
          >
            <div
              class="h-full rounded-full bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-1000 ease-out"
              style="width: {isCompleted
                ? 100
                : Math.max(
                    0,
                    Math.min(
                      100,
                      ((Date.now() - (targetDate.getTime() - 7 * 24 * 60 * 60 * 1_000)) /
                        (7 * 24 * 60 * 60 * 1_000)) *
                        100,
                    ),
                  )}%"
            ></div>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
