<script lang="ts">
  import FileDropzone from '$lib/components/FileDropzone.svelte';
  import FileQueue from '$lib/components/FileQueue.svelte';
  import InvoiceResults from '$lib/components/InvoiceResults.svelte';
  import { fileQueueStore } from '$lib/stores/fileQueue.js';
  import { selectedFileStore } from '$lib/stores/selectedFile.js';
  import { processingStore } from '$lib/stores/processing.js';

  // Destructure stores for rune access
  const { hasFiles } = fileQueueStore;
  const { hasSelection } = selectedFileStore;
  const { hasErrors, isShowingErrorPanel, recentErrors, errorLogs, errorCount } = processingStore;

  function toggleErrorPanel() {
    processingStore.toggleErrorPanel();
  }
</script>

<svelte:head>
  <title>Invoice PDF Processor</title>
  <meta name="description" content="Process invoice PDFs and extract structured data using AI" />
</svelte:head>

<div class="app-container">
  <header class="app-header">
    <div class="header-content">
      <h1>Invoice PDF Processor</h1>
      <p>Upload PDF invoices to extract supplier, dates, and amounts automatically</p>
    </div>

    {#if $hasErrors}
      <button
        class="error-toggle"
        class:active={$isShowingErrorPanel}
        onclick={toggleErrorPanel}
        title="View error logs"
      >
        🚨 {$errorCount} errors
      </button>
    {/if}
  </header>

  <main class="app-main">
    <div class="sidebar">
      <FileQueue />
    </div>

    <div class="content">
      {#if !$hasFiles}
        <div class="welcome-section">
          <FileDropzone maxFiles={10} />
          <div class="info-section">
            <h2>How it works</h2>
            <ol>
              <li>
                <strong>Upload PDFs</strong>
                <span>Drag and drop or select PDF invoice files (max 10MB each)</span>
              </li>
              <li>
                <strong>Automatic Processing</strong>
                <span>AI extracts text and identifies key invoice data</span>
              </li>
              <li>
                <strong>Review Results</strong>
                <span>View extracted supplier, dates, amounts, and metadata</span>
              </li>
            </ol>

            <div class="supported-languages">
              <h3>Supported Languages</h3>
              <p>🇬🇧 English • 🇳🇱 Dutch</p>
            </div>
          </div>
        </div>
      {:else if $hasSelection}
        <InvoiceResults />
      {:else}
        <div class="no-selection">
          <div class="icon">👆</div>
          <h3>Select a file from the queue</h3>
          <p>Click on any file in the queue to view its processing status and results</p>

          <div class="quick-actions">
            <h4>Quick Actions</h4>
            <div class="action-buttons">
              <button class="action-btn" onclick={() => fileQueueStore.processQueue()}>
                ▶️ Start Processing
              </button>
              <button class="action-btn" onclick={() => fileQueueStore.clearProcessed()}>
                🗑️ Clear Processed
              </button>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </main>

  {#if $isShowingErrorPanel}
    <div class="error-panel">
      <div class="error-header">
        <h3>Error Logs</h3>
        <button class="close-btn" onclick={() => processingStore.hideErrorPanel()}>×</button>
      </div>
      <div class="error-list">
        {#each $recentErrors as error (error.timestamp)}
          <div class="error-item">
            <div class="error-meta">
              <span class="error-time">{new Date(error.timestamp).toLocaleString()}</span>
              <span class="error-stage">{error.stage}</span>
              <span class="error-file">{error.fileName}</span>
            </div>
            <div class="error-message">{error.error}</div>
          </div>
        {/each}

        {#if $errorLogs.length === 0}
          <div class="no-errors">
            <p>No errors logged yet</p>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>

<style>
  :global(body) {
    margin: 0;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #f8fafc;
  }

  .app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .app-header {
    background: white;
    border-bottom: 2px solid #e2e8f0;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-content h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
  }

  .header-content p {
    margin: 0;
    color: #718096;
    font-size: 1.125rem;
  }

  .error-toggle {
    background: #fed7d7;
    color: #c53030;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .error-toggle:hover,
  .error-toggle.active {
    background: #feb2b2;
  }

  .app-main {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .sidebar {
    width: 350px;
    flex-shrink: 0;
    background: white;
    overflow: hidden;
  }

  .content {
    flex: 1;
    background: #f8fafc;
    overflow: hidden;
  }

  .welcome-section {
    height: 100%;
    padding: 2rem;
    overflow-y: auto;
  }

  .info-section {
    max-width: 600px;
    margin: 2rem auto;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
  }

  .info-section h2 {
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
  }

  .info-section ol {
    list-style: none;
    counter-reset: step-counter;
    padding: 0;
    margin: 0 0 2rem 0;
  }

  .info-section li {
    counter-increment: step-counter;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #4299e1;
    position: relative;
  }

  .info-section li::before {
    content: counter(step-counter);
    position: absolute;
    left: -2px;
    top: -8px;
    background: #4299e1;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
  }

  .info-section li strong {
    color: #2d3748;
    font-weight: 600;
  }

  .info-section li span {
    color: #4a5568;
    font-size: 0.875rem;
  }

  .supported-languages {
    border-top: 1px solid #e2e8f0;
    padding-top: 1.5rem;
    text-align: center;
  }

  .supported-languages h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: #4a5568;
  }

  .supported-languages p {
    margin: 0;
    font-size: 1.25rem;
  }

  .no-selection {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
  }

  .no-selection .icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
  }

  .no-selection h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
  }

  .no-selection p {
    margin: 0 0 2rem 0;
    color: #718096;
    max-width: 400px;
  }

  .quick-actions {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
  }

  .quick-actions h4 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: #4a5568;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .action-btn {
    background: white;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .action-btn:hover {
    border-color: #4299e1;
    background: #ebf8ff;
    color: #2b6cb0;
  }

  .error-panel {
    position: absolute;
    bottom: 0;
    right: 1rem;
    width: 400px;
    max-height: 300px;
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    z-index: 1000;
  }

  .error-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    background: #fed7d7;
  }

  .error-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #c53030;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #c53030;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
  }

  .close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  .error-list {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
  }

  .error-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #fef5e7;
    border-radius: 6px;
    border-left: 4px solid #f6ad55;
  }

  .error-meta {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
    color: #744210;
  }

  .error-time,
  .error-stage,
  .error-file {
    background: rgba(116, 66, 16, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
  }

  .error-message {
    font-size: 0.875rem;
    color: #744210;
    font-family: monospace;
    word-break: break-word;
  }

  .no-errors {
    text-align: center;
    padding: 2rem;
    color: #718096;
  }

  @media (max-width: 768px) {
    .app-header {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .app-main {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: 300px;
    }

    .error-panel {
      right: 0.5rem;
      width: calc(100% - 1rem);
      max-width: 400px;
    }

    .action-buttons {
      flex-direction: column;
      align-items: center;
    }

    .action-btn {
      width: 100%;
      max-width: 200px;
    }
  }
</style>
